"""
Test script for JarvisTrade Lite
"""
import os
import asyncio
import logging
from dotenv import load_dotenv
from rich.console import Console

from jarvistrade.config import config
from jarvistrade.api.binance import BinanceClient
from jarvistrade.api.websocket import BinanceWebSocket, OrderBookManager
from jarvistrade.models.spoof_detector import SpoofDetector
from jarvistrade.strategies.anti_spoof import AntiSpoofStrategy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("test_bot.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
console = Console()

async def test_order_book():
    """Test order book functionality"""
    console.print("[bold cyan]Testing Order Book Functionality[/]")
    
    # Initialize components
    binance_client = BinanceClient(testnet=True)
    order_book_manager = OrderBookManager(symbols=["BTCUSDT"])
    
    # Initialize order books
    console.print("Initializing order books...")
    await order_book_manager.initialize_order_books()
    
    # Get order book
    order_book = order_book_manager.get_order_book("BTCUSDT")
    
    # Print order book summary
    console.print("[bold green]Order Book Summary:[/]")
    best_bid_ask = order_book_manager.get_best_bid_ask("BTCUSDT")
    console.print(f"Best Bid: {best_bid_ask['bid_price']:.2f} ({best_bid_ask['bid_qty']:.5f})")
    console.print(f"Best Ask: {best_bid_ask['ask_price']:.2f} ({best_bid_ask['ask_qty']:.5f})")
    console.print(f"Spread: {best_bid_ask['spread']:.2f} ({best_bid_ask['spread_percent']:.2f}%)")
    
    return True

async def test_spoof_detector():
    """Test spoof detector functionality"""
    console.print("\n[bold cyan]Testing Spoof Detector[/]")
    
    # Initialize components
    order_book_manager = OrderBookManager(symbols=["BTCUSDT"])
    spoof_detector = SpoofDetector()
    
    # Initialize order books
    console.print("Initializing order books...")
    await order_book_manager.initialize_order_books()
    
    # Get order book
    order_book = order_book_manager.get_order_book("BTCUSDT")
    
    # Detect spoofing
    console.print("Detecting spoofing...")
    detection = spoof_detector.detect_spoof("BTCUSDT", order_book)
    
    # Print detection results
    console.print("[bold green]Spoof Detection Results:[/]")
    console.print(f"Probability: {detection['spoof_probability']:.2f}")
    console.print(f"Side: {detection['side'] or 'None'}")
    
    # Print features
    console.print("[bold green]Order Book Features:[/]")
    for key, value in detection['features'].items():
        if isinstance(value, float):
            console.print(f"{key}: {value:.4f}")
        else:
            console.print(f"{key}: {value}")
    
    return True

async def test_strategy():
    """Test trading strategy functionality"""
    console.print("\n[bold cyan]Testing Trading Strategy[/]")
    
    # Initialize components
    binance_client = BinanceClient(testnet=True)
    order_book_manager = OrderBookManager(symbols=["BTCUSDT"])
    spoof_detector = SpoofDetector()
    strategy = AntiSpoofStrategy(spoof_detector, binance_client)
    
    # Initialize order books
    console.print("Initializing order books...")
    await order_book_manager.initialize_order_books()
    
    # Get order book
    order_book = order_book_manager.get_order_book("BTCUSDT")
    
    # Process order book with strategy
    console.print("Processing order book with strategy...")
    signal = strategy.process_order_book("BTCUSDT", order_book)
    
    # Print signal
    console.print("[bold green]Trading Signal:[/]")
    if signal:
        console.print(f"Symbol: {signal['symbol']}")
        console.print(f"Action: {signal['action']}")
        console.print(f"Price: {signal['price']:.2f}")
        console.print(f"Spoof Probability: {signal['spoof_probability']:.2f}")
        console.print(f"Spoof Side: {signal['spoof_side']}")
    else:
        console.print("No trading signal generated")
    
    return True

async def main():
    """Main test function"""
    console.print("[bold cyan]JarvisTrade Lite Test Script[/]", justify="center")
    console.print("Testing core functionality", justify="center")
    console.print("")
    
    # Load environment variables
    load_dotenv()
    
    # Check if API keys are set
    if not config.api_key or not config.api_secret:
        console.print("[bold red]API keys not set in .env file[/]")
        console.print("Please set BINANCE_API_KEY and BINANCE_API_SECRET in .env file")
        return False
    
    # Run tests
    try:
        # Test order book
        await test_order_book()
        
        # Test spoof detector
        await test_spoof_detector()
        
        # Test strategy
        await test_strategy()
        
        console.print("\n[bold green]All tests completed successfully![/]")
        return True
    
    except Exception as e:
        console.print(f"[bold red]Error: {e}[/]")
        logger.exception("Unhandled exception")
        return False

if __name__ == "__main__":
    asyncio.run(main())
