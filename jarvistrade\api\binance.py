"""
Binance API client for JarvisTrade Lite
"""
import time
import hmac
import hashlib
import requests
from typing import Dict, List, Any, Optional, Union
from urllib.parse import urlencode
import logging

from jarvistrade.config import config
from jarvistrade.utils.cache import api_cache
from jarvistrade.utils.performance import performance_monitor, timed

logger = logging.getLogger(__name__)

class BinanceClient:
    """Binance API client for REST endpoints"""

    def __init__(self, api_key: str = "", api_secret: str = "", testnet: bool = True):
        """
        Initialize Binance API client

        Args:
            api_key: Binance API key
            api_secret: Binance API secret
            testnet: Use testnet if True
        """
        self.api_key = api_key or config.api_key
        self.api_secret = api_secret or config.api_secret
        self.testnet = testnet or config.use_testnet

        # API URLs
        if self.testnet:
            self.base_url = "https://testnet.binance.vision/api"
        else:
            self.base_url = "https://api.binance.com/api"

        # WebSocket URLs
        if self.testnet:
            self.ws_url = "wss://testnet.binance.vision/ws"
        else:
            self.ws_url = "wss://stream.binance.com:9443/ws"

    def _generate_signature(self, params: Dict[str, Any]) -> str:
        """
        Generate HMAC signature for API request

        Args:
            params: Request parameters

        Returns:
            str: HMAC signature
        """
        query_string = urlencode(params)
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return signature

    def _request(self, method: str, endpoint: str,
                 signed: bool = False,
                 params: Optional[Dict[str, Any]] = None,
                 retry_count: int = 3,
                 retry_delay: float = 1.0) -> Dict[str, Any]:
        """
        Make API request to Binance with retry logic

        Args:
            method: HTTP method (GET, POST, DELETE)
            endpoint: API endpoint
            signed: Whether request needs signature
            params: Request parameters
            retry_count: Number of retries on failure
            retry_delay: Delay between retries in seconds

        Returns:
            Dict[str, Any]: API response

        Raises:
            requests.exceptions.RequestException: If request fails after all retries
        """
        url = f"{self.base_url}{endpoint}"
        headers = {"X-MBX-APIKEY": self.api_key}

        if params is None:
            params = {}

        # Add timestamp and signature for signed requests
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            params['signature'] = self._generate_signature(params)

        # Initialize retry counter
        retries = 0
        last_exception = None

        # Retry loop
        while retries <= retry_count:
            try:
                # Make request
                if method == "GET":
                    response = requests.get(url, headers=headers, params=params, timeout=10)
                elif method == "POST":
                    response = requests.post(url, headers=headers, params=params, timeout=10)
                elif method == "DELETE":
                    response = requests.delete(url, headers=headers, params=params, timeout=10)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                # Check for API errors
                if response.status_code == 429:
                    # Rate limit exceeded
                    retry_after = int(response.headers.get('Retry-After', retry_delay * 2))
                    logger.warning(f"Rate limit exceeded. Retrying after {retry_after} seconds")
                    time.sleep(retry_after)
                    retries += 1
                    continue

                # Check for other HTTP errors
                response.raise_for_status()

                # Parse JSON response
                data = response.json()

                # Check for API error codes in response
                if isinstance(data, dict) and 'code' in data and data['code'] != 0:
                    error_msg = data.get('msg', 'Unknown API error')
                    logger.error(f"API error: {error_msg} (code: {data['code']})")

                    # Handle specific error codes
                    if data['code'] == -1021:  # INVALID_TIMESTAMP
                        # Timestamp too far from server time, retry with updated timestamp
                        if signed and retries < retry_count:
                            params['timestamp'] = int(time.time() * 1000)
                            params['signature'] = self._generate_signature(params)
                            retries += 1
                            continue

                    # Raise exception for other API errors
                    raise requests.exceptions.RequestException(f"API error: {error_msg} (code: {data['code']})")

                # Return successful response
                return data

            except requests.exceptions.RequestException as e:
                last_exception = e
                logger.error(f"API request error: {e}")

                # Log response text if available
                if hasattr(e, 'response') and e.response is not None:
                    logger.error(f"Response: {e.response.text}")

                # Check if we should retry
                if retries < retry_count:
                    # Exponential backoff
                    sleep_time = retry_delay * (2 ** retries)
                    logger.info(f"Retrying in {sleep_time:.2f} seconds (attempt {retries + 1}/{retry_count})")
                    time.sleep(sleep_time)
                    retries += 1
                else:
                    # Max retries reached, re-raise the exception
                    logger.error(f"Max retries reached for {endpoint}")
                    raise

            except Exception as e:
                # Catch other exceptions (JSON parsing, etc.)
                logger.error(f"Unexpected error in API request: {e}")
                if retries < retry_count:
                    time.sleep(retry_delay)
                    retries += 1
                else:
                    raise

        # This should not be reached, but just in case
        if last_exception:
            raise last_exception
        else:
            raise Exception("Unknown error in API request")

    @timed(category="api")
    def get_exchange_info(self) -> Dict[str, Any]:
        """
        Get exchange information

        Returns:
            Dict[str, Any]: Exchange information
        """
        performance_monitor.increment_counter("api_calls_exchange_info")
        return self._request("GET", "/v3/exchangeInfo")

    @timed(category="api")
    def get_account(self) -> Dict[str, Any]:
        """
        Get account information

        Returns:
            Dict[str, Any]: Account information
        """
        performance_monitor.increment_counter("api_calls_account")
        return self._request("GET", "/v3/account", signed=True)

    def get_order_book(self, symbol: str, limit: int = 10) -> Dict[str, Any]:
        """
        Get order book for symbol

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            limit: Number of price levels (5, 10, 20, 50, 100, 500, 1000, 5000)

        Returns:
            Dict[str, Any]: Order book
        """
        params = {"symbol": symbol, "limit": limit}
        return self._request("GET", "/v3/depth", params=params)

    @timed(category="trading")
    def create_order(self, symbol: str, side: str, order_type: str,
                     quantity: Optional[float] = None,
                     price: Optional[float] = None,
                     time_in_force: str = "GTC",
                     stop_price: Optional[float] = None,
                     iceberg_qty: Optional[float] = None) -> Dict[str, Any]:
        """
        Create a new order

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            side: Order side (BUY or SELL)
            order_type: Order type (LIMIT, MARKET, STOP_LOSS, etc.)
            quantity: Order quantity
            price: Order price (required for LIMIT orders)
            time_in_force: Time in force (GTC, IOC, FOK)
            stop_price: Stop price (required for STOP_LOSS orders)
            iceberg_qty: Iceberg quantity

        Returns:
            Dict[str, Any]: Order information
        """
        params = {
            "symbol": symbol,
            "side": side,
            "type": order_type,
        }

        if quantity is not None:
            params["quantity"] = quantity

        if price is not None:
            params["price"] = price

        if order_type == "LIMIT":
            params["timeInForce"] = time_in_force

        if stop_price is not None:
            params["stopPrice"] = stop_price

        if iceberg_qty is not None:
            params["icebergQty"] = iceberg_qty

        performance_monitor.increment_counter("orders_created")
        return self._request("POST", "/v3/order", signed=True, params=params)

    def cancel_order(self, symbol: str, order_id: Optional[int] = None,
                     orig_client_order_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Cancel an order

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            order_id: Order ID
            orig_client_order_id: Original client order ID

        Returns:
            Dict[str, Any]: Cancellation information
        """
        params = {"symbol": symbol}

        if order_id is not None:
            params["orderId"] = order_id

        if orig_client_order_id is not None:
            params["origClientOrderId"] = orig_client_order_id

        return self._request("DELETE", "/v3/order", signed=True, params=params)

    def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get open orders

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)

        Returns:
            List[Dict[str, Any]]: Open orders
        """
        params = {}
        if symbol is not None:
            params["symbol"] = symbol

        return self._request("GET", "/v3/openOrders", signed=True, params=params)

    def get_order(self, symbol: str, order_id: Optional[int] = None,
                  orig_client_order_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get order status

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            order_id: Order ID
            orig_client_order_id: Original client order ID

        Returns:
            Dict[str, Any]: Order information
        """
        params = {"symbol": symbol}

        if order_id is not None:
            params["orderId"] = order_id

        if orig_client_order_id is not None:
            params["origClientOrderId"] = orig_client_order_id

        return self._request("GET", "/v3/order", signed=True, params=params)
