"""
Order execution and management for JarvisTrade Lite
"""
import logging
import time
import csv
import os
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import threading

from jarvistrade.config import config
from jarvistrade.api.binance import BinanceClient
from jarvistrade.alerts import AlertManager
from jarvistrade.risk import RiskManager

logger = logging.getLogger(__name__)

class OrderManager:
    """Manages order execution and tracking"""

    def __init__(self, binance_client: BinanceClient,
                 on_order_update_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
                 alert_manager: Optional[AlertManager] = None,
                 risk_manager: Optional[RiskManager] = None):
        """
        Initialize order manager

        Args:
            binance_client: Binance API client
            on_order_update_callback: Callback for order updates
            alert_manager: Alert manager for notifications
            risk_manager: Risk manager for advanced risk control
        """
        self.binance_client = binance_client
        self.on_order_update_callback = on_order_update_callback
        self.alert_manager = alert_manager or AlertManager()
        self.risk_manager = risk_manager or RiskManager()
        self.active_orders = {}  # {order_id: order_info}
        self.trade_history = []  # List of completed trades
        self.pnl = 0.0  # Overall PNL
        self.monitoring_thread = None
        self.is_monitoring = False

    def execute_signal(self, signal: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Execute trading signal with enhanced security validations

        Args:
            signal: Trading signal

        Returns:
            Optional[Dict[str, Any]]: Order information if successful
        """
        try:
            symbol = signal["symbol"]
            side = signal["action"]
            price = signal["price"]

            # Risk management check
            can_trade, risk_reason = self.risk_manager.can_trade()
            if not can_trade:
                logger.warning(f"Trade blocked by risk manager: {risk_reason}")
                self.alert_manager.alert_error(f"Trade blocked: {risk_reason}")
                return None

            # Security validations
            if not self._validate_trading_conditions(symbol, side, price):
                return None

            # Calculate position size
            quantity = self._calculate_position_size(symbol, price)

            if quantity <= 0:
                logger.warning(f"Invalid position size for {symbol}: {quantity}")
                return None

            # Final validation before placing order
            if not self._validate_order_parameters(symbol, side, quantity):
                return None

            # Place market order
            order = self.binance_client.create_order(
                symbol=symbol,
                side=side,
                order_type="MARKET",
                quantity=quantity
            )

            # Store order information
            order_info = {
                "order_id": order["orderId"],
                "symbol": symbol,
                "side": side,
                "quantity": float(order["executedQty"]),
                "price": float(order["price"]) if "price" in order else price,
                "status": order["status"],
                "timestamp": datetime.now().isoformat(),
                "signal": signal,
                "stop_loss": None,
                "take_profit": None
            }

            self.active_orders[order["orderId"]] = order_info

            # Place take profit and stop loss orders
            self._place_take_profit_stop_loss(order_info)

            # Start monitoring if not already running
            self._start_monitoring()

            # Call order update callback
            if self.on_order_update_callback:
                self.on_order_update_callback(order_info)

            logger.info(f"Executed order for {symbol}: {side} {quantity} at {price}")

            # Send alert
            self.alert_manager.alert_trade_executed(symbol, side, quantity, price)

            return order_info

        except Exception as e:
            logger.error(f"Error executing signal: {e}")
            return None

    def _calculate_position_size(self, symbol: str, price: float) -> float:
        """
        Calculate position size based on risk parameters

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            price: Entry price

        Returns:
            float: Position size in base currency
        """
        try:
            # Get account balance
            account = self.binance_client.get_account()

            # Find USDT balance
            usdt_balance = 0
            for asset in account["balances"]:
                if asset["asset"] == "USDT":
                    usdt_balance = float(asset["free"])
                    break

            # Calculate position size
            risk_amount = usdt_balance * (config.risk_per_trade_percent / 100)

            # Limit to max position size
            position_size_usd = min(risk_amount, config.max_position_size_usd)

            # Convert to base currency
            position_size = position_size_usd / price

            # Apply risk management multiplier
            risk_multiplier = self.risk_manager.get_position_size_multiplier()
            position_size *= risk_multiplier

            # Update risk manager with current balance
            self.risk_manager.update_balance(usdt_balance)

            # Round to appropriate precision
            # TODO: Get symbol precision from exchange info
            position_size = round(position_size, 5)

            logger.info(f"Position size calculated: {position_size} (risk multiplier: {risk_multiplier:.2f})")
            return position_size

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0

    def _place_take_profit_stop_loss(self, order_info: Dict[str, Any]) -> None:
        """
        Place take profit and stop loss orders

        Args:
            order_info: Order information
        """
        try:
            symbol = order_info["symbol"]
            side = order_info["side"]
            quantity = order_info["quantity"]
            price = order_info["price"]

            # Calculate take profit and stop loss prices
            if side == "BUY":
                tp_price = price * (1 + config.take_profit_percent / 100)
                sl_price = price * (1 - config.stop_loss_percent / 100)
                tp_side = "SELL"
                sl_side = "SELL"
            else:
                tp_price = price * (1 - config.take_profit_percent / 100)
                sl_price = price * (1 + config.stop_loss_percent / 100)
                tp_side = "BUY"
                sl_side = "BUY"

            # Round prices to appropriate precision
            # TODO: Get symbol precision from exchange info
            tp_price = round(tp_price, 2)
            sl_price = round(sl_price, 2)

            # Place take profit order
            tp_order = self.binance_client.create_order(
                symbol=symbol,
                side=tp_side,
                order_type="LIMIT",
                time_in_force="GTC",
                quantity=quantity,
                price=tp_price
            )

            # Place stop loss order
            sl_order = self.binance_client.create_order(
                symbol=symbol,
                side=sl_side,
                order_type="STOP_LOSS_LIMIT",
                time_in_force="GTC",
                quantity=quantity,
                price=sl_price,
                stop_price=sl_price
            )

            # Update order info with TP and SL orders
            order_info["tp_order_id"] = tp_order["orderId"]
            order_info["sl_order_id"] = sl_order["orderId"]
            order_info["tp_price"] = tp_price
            order_info["sl_price"] = sl_price

            logger.info(f"Placed TP/SL for {symbol}: TP at {tp_price}, SL at {sl_price}")

        except Exception as e:
            logger.error(f"Error placing TP/SL orders: {e}")

    def _start_monitoring(self) -> None:
        """Start monitoring thread for active orders"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._monitor_orders)
            self.monitoring_thread.daemon = True
            self.monitoring_thread.start()

    def _monitor_orders(self) -> None:
        """Monitor active orders"""
        while self.is_monitoring and self.active_orders:
            try:
                # Check all active orders
                for order_id, order_info in list(self.active_orders.items()):
                    symbol = order_info["symbol"]

                    # Check main order status
                    order_status = self.binance_client.get_order(
                        symbol=symbol,
                        order_id=order_id
                    )

                    # Update order status
                    order_info["status"] = order_status["status"]

                    # Check if order is filled
                    if order_status["status"] == "FILLED":
                        # Check TP and SL orders
                        if "tp_order_id" in order_info:
                            tp_status = self.binance_client.get_order(
                                symbol=symbol,
                                order_id=order_info["tp_order_id"]
                            )

                            if tp_status["status"] == "FILLED":
                                # TP hit, cancel SL
                                if "sl_order_id" in order_info:
                                    self.binance_client.cancel_order(
                                        symbol=symbol,
                                        order_id=order_info["sl_order_id"]
                                    )

                                # Calculate PNL
                                entry_price = float(order_status["price"])
                                exit_price = float(tp_status["price"])
                                quantity = float(order_status["executedQty"])

                                if order_info["side"] == "BUY":
                                    trade_pnl = (exit_price - entry_price) * quantity
                                else:
                                    trade_pnl = (entry_price - exit_price) * quantity

                                # Update PNL
                                self.pnl += trade_pnl

                                # Add to trade history
                                trade = {
                                    "symbol": symbol,
                                    "side": order_info["side"],
                                    "entry_price": entry_price,
                                    "exit_price": exit_price,
                                    "quantity": quantity,
                                    "pnl": trade_pnl,
                                    "exit_type": "TP",
                                    "entry_time": order_info["timestamp"],
                                    "exit_time": datetime.now().isoformat()
                                }

                                self.trade_history.append(trade)

                                # Record trade in risk manager
                                self.risk_manager.record_trade(trade)

                                # Send trade completion alert
                                self.alert_manager.alert_trade_completed(symbol, trade_pnl, "TP")

                                # Export trade to CSV
                                self._export_trade_to_csv(trade)

                                # Remove from active orders
                                del self.active_orders[order_id]

                                # Call order update callback
                                if self.on_order_update_callback:
                                    self.on_order_update_callback({
                                        "type": "trade_completed",
                                        "trade": trade
                                    })

                                logger.info(f"TP hit for {symbol}: {trade_pnl} profit")

                        elif "sl_order_id" in order_info:
                            sl_status = self.binance_client.get_order(
                                symbol=symbol,
                                order_id=order_info["sl_order_id"]
                            )

                            if sl_status["status"] == "FILLED":
                                # SL hit, cancel TP
                                if "tp_order_id" in order_info:
                                    self.binance_client.cancel_order(
                                        symbol=symbol,
                                        order_id=order_info["tp_order_id"]
                                    )

                                # Calculate PNL
                                entry_price = float(order_status["price"])
                                exit_price = float(sl_status["price"])
                                quantity = float(order_status["executedQty"])

                                if order_info["side"] == "BUY":
                                    trade_pnl = (exit_price - entry_price) * quantity
                                else:
                                    trade_pnl = (entry_price - exit_price) * quantity

                                # Update PNL
                                self.pnl += trade_pnl

                                # Add to trade history
                                trade = {
                                    "symbol": symbol,
                                    "side": order_info["side"],
                                    "entry_price": entry_price,
                                    "exit_price": exit_price,
                                    "quantity": quantity,
                                    "pnl": trade_pnl,
                                    "exit_type": "SL",
                                    "entry_time": order_info["timestamp"],
                                    "exit_time": datetime.now().isoformat()
                                }

                                self.trade_history.append(trade)

                                # Record trade in risk manager
                                self.risk_manager.record_trade(trade)

                                # Send trade completion alert
                                self.alert_manager.alert_trade_completed(symbol, trade_pnl, "SL")

                                # Export trade to CSV
                                self._export_trade_to_csv(trade)

                                # Remove from active orders
                                del self.active_orders[order_id]

                                # Call order update callback
                                if self.on_order_update_callback:
                                    self.on_order_update_callback({
                                        "type": "trade_completed",
                                        "trade": trade
                                    })

                                logger.info(f"SL hit for {symbol}: {trade_pnl} loss")

                # Sleep to avoid API rate limits
                time.sleep(5)

            except Exception as e:
                logger.error(f"Error monitoring orders: {e}")
                time.sleep(10)

        self.is_monitoring = False

    def _export_trade_to_csv(self, trade: Dict[str, Any]) -> None:
        """
        Export trade to CSV file

        Args:
            trade: Trade information
        """
        try:
            # Create trades directory if it doesn't exist
            os.makedirs("trades", exist_ok=True)

            # CSV file path
            csv_path = "trades/trade_history.csv"

            # Check if file exists
            file_exists = os.path.isfile(csv_path)

            # Write to CSV
            with open(csv_path, "a", newline="") as f:
                writer = csv.DictWriter(f, fieldnames=trade.keys())

                if not file_exists:
                    writer.writeheader()

                writer.writerow(trade)

            logger.info(f"Exported trade to {csv_path}")

        except Exception as e:
            logger.error(f"Error exporting trade to CSV: {e}")

    def get_pnl(self) -> float:
        """
        Get current PNL

        Returns:
            float: Current PNL
        """
        return self.pnl

    def get_trade_history(self) -> List[Dict[str, Any]]:
        """
        Get trade history

        Returns:
            List[Dict[str, Any]]: Trade history
        """
        return self.trade_history

    def get_active_orders(self) -> Dict[str, Dict[str, Any]]:
        """
        Get active orders

        Returns:
            Dict[str, Dict[str, Any]]: Active orders
        """
        return self.active_orders

    def get_risk_stats(self) -> Dict[str, Any]:
        """
        Get risk management statistics

        Returns:
            Dict[str, Any]: Risk management stats
        """
        return self.risk_manager.get_stats()

    def _validate_trading_conditions(self, symbol: str, side: str, price: float) -> bool:
        """
        Validate trading conditions before placing order

        Args:
            symbol: Trading pair symbol
            side: Order side (BUY/SELL)
            price: Entry price

        Returns:
            bool: True if conditions are valid
        """
        try:
            # Check if symbol is in allowed trading pairs
            if symbol not in config.trading_pairs:
                logger.error(f"Symbol {symbol} not in allowed trading pairs: {config.trading_pairs}")
                return False

            # Check if side is valid
            if side not in ["BUY", "SELL"]:
                logger.error(f"Invalid order side: {side}")
                return False

            # Check if price is reasonable (not zero or negative)
            if price <= 0:
                logger.error(f"Invalid price: {price}")
                return False

            # Check account balance
            account = self.binance_client.get_account()
            usdt_balance = 0
            for asset in account["balances"]:
                if asset["asset"] == "USDT":
                    usdt_balance = float(asset["free"])
                    break

            # Check if we have sufficient balance
            min_balance = config.max_position_size_usd * 0.1  # At least 10% of max position
            if usdt_balance < min_balance:
                logger.error(f"Insufficient USDT balance: {usdt_balance} < {min_balance}")
                return False

            # Check if we have too many active orders for this symbol
            symbol_orders = [order for order in self.active_orders.values() if order["symbol"] == symbol]
            if len(symbol_orders) >= 3:  # Max 3 active orders per symbol
                logger.warning(f"Too many active orders for {symbol}: {len(symbol_orders)}")
                return False

            # Check exchange info for symbol status
            exchange_info = self.binance_client.get_exchange_info()
            symbol_info = None
            for s in exchange_info["symbols"]:
                if s["symbol"] == symbol:
                    symbol_info = s
                    break

            if not symbol_info:
                logger.error(f"Symbol {symbol} not found in exchange info")
                return False

            if symbol_info["status"] != "TRADING":
                logger.error(f"Symbol {symbol} is not in TRADING status: {symbol_info['status']}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating trading conditions: {e}")
            return False

    def _validate_order_parameters(self, symbol: str, side: str, quantity: float) -> bool:
        """
        Validate order parameters before placing order

        Args:
            symbol: Trading pair symbol
            side: Order side (BUY/SELL)
            quantity: Order quantity

        Returns:
            bool: True if parameters are valid
        """
        try:
            # Get symbol info for filters
            exchange_info = self.binance_client.get_exchange_info()
            symbol_info = None
            for s in exchange_info["symbols"]:
                if s["symbol"] == symbol:
                    symbol_info = s
                    break

            if not symbol_info:
                logger.error(f"Symbol {symbol} not found in exchange info")
                return False

            # Check quantity against LOT_SIZE filter
            for filter_info in symbol_info["filters"]:
                if filter_info["filterType"] == "LOT_SIZE":
                    min_qty = float(filter_info["minQty"])
                    max_qty = float(filter_info["maxQty"])
                    step_size = float(filter_info["stepSize"])

                    if quantity < min_qty:
                        logger.error(f"Quantity {quantity} below minimum {min_qty} for {symbol}")
                        return False

                    if quantity > max_qty:
                        logger.error(f"Quantity {quantity} above maximum {max_qty} for {symbol}")
                        return False

                    # Check step size
                    if step_size > 0:
                        remainder = (quantity - min_qty) % step_size
                        if remainder != 0:
                            logger.error(f"Quantity {quantity} does not match step size {step_size} for {symbol}")
                            return False

            return True

        except Exception as e:
            logger.error(f"Error validating order parameters: {e}")
            return False
