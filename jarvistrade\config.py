"""
Configuration management for JarvisTrade Lite
"""
import os
import logging
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

class Config:
    """Configuration manager for JarvisTrade Lite"""

    def __init__(self, env_file: str = ".env"):
        """
        Initialize configuration from environment variables

        Args:
            env_file: Path to .env file
        """
        # Load environment variables from .env file
        load_dotenv(env_file)

        # API credentials
        self.api_key: str = os.getenv("BINANCE_API_KEY", "")
        self.api_secret: str = os.getenv("BINANCE_API_SECRET", "")

        # Trading settings
        self.trading_pairs: List[str] = os.getenv("TRADING_PAIRS", "BTCUSDT").split(",")
        self.use_testnet: bool = os.getenv("USE_TESTNET", "True").lower() == "true"
        self.max_position_size_usd: float = float(os.getenv("MAX_POSITION_SIZE_USD", "100"))
        self.risk_per_trade_percent: float = float(os.getenv("RISK_PER_TRADE_PERCENT", "1"))
        self.take_profit_percent: float = float(os.getenv("TAKE_PROFIT_PERCENT", "0.25"))
        self.stop_loss_percent: float = float(os.getenv("STOP_LOSS_PERCENT", "0.15"))

        # Spoof detection settings
        self.spoof_threshold: float = float(os.getenv("SPOOF_THRESHOLD", "0.85"))
        self.min_spread_entry: float = float(os.getenv("MIN_SPREAD_ENTRY", "0.08"))

    def validate(self) -> bool:
        """
        Validate configuration

        Returns:
            bool: True if configuration is valid
        """
        if not self.api_key or not self.api_secret:
            return False

        if not self.trading_pairs:
            return False

        return True

    def load_profile(self, profile_name: str) -> bool:
        """
        Load configuration from a trading profile

        Args:
            profile_name: Name of the profile to load

        Returns:
            bool: True if profile was loaded successfully
        """
        try:
            from jarvistrade.config.profiles import profile_manager

            if profile_manager.set_current_profile(profile_name):
                profile_manager.apply_profile_to_config(self)
                logger.info(f"Loaded profile: {profile_name}")
                return True
            else:
                logger.error(f"Failed to load profile: {profile_name}")
                return False

        except Exception as e:
            logger.error(f"Error loading profile {profile_name}: {e}")
            return False

    def get_available_profiles(self) -> Dict[str, str]:
        """
        Get available trading profiles

        Returns:
            Dict[str, str]: Profile names and descriptions
        """
        try:
            from jarvistrade.config.profiles import profile_manager
            return profile_manager.list_profiles()
        except Exception as e:
            logger.error(f"Error getting profiles: {e}")
            return {}

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary

        Returns:
            Dict[str, Any]: Configuration as dictionary
        """
        return {
            "api_key": self.api_key[:5] + "..." if self.api_key else "",
            "api_secret": "..." if self.api_secret else "",
            "trading_pairs": self.trading_pairs,
            "use_testnet": self.use_testnet,
            "max_position_size_usd": self.max_position_size_usd,
            "risk_per_trade_percent": self.risk_per_trade_percent,
            "take_profit_percent": self.take_profit_percent,
            "stop_loss_percent": self.stop_loss_percent,
            "spoof_threshold": self.spoof_threshold,
            "min_spread_entry": self.min_spread_entry,
        }

    def __str__(self) -> str:
        """String representation of configuration"""
        config_dict = self.to_dict()
        return "\n".join(f"{k}: {v}" for k, v in config_dict.items())


# Global configuration instance
config = Config()
