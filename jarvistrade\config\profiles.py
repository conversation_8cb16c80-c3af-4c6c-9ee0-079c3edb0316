"""
Configuration profiles for JarvisTrade Lite
"""
import os
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class ProfileType(Enum):
    """Configuration profile types"""
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    AGGRESSIVE = "aggressive"
    CUSTOM = "custom"


@dataclass
class TradingProfile:
    """Trading configuration profile"""
    name: str
    description: str
    
    # Risk settings
    max_position_size_usd: float
    risk_per_trade_percent: float
    take_profit_percent: float
    stop_loss_percent: float
    
    # Spoof detection
    spoof_threshold: float
    min_spread_entry: float
    
    # Risk management
    max_daily_drawdown_percent: float
    max_total_drawdown_percent: float
    max_daily_trades: int
    max_hourly_trades: int
    max_consecutive_losses: int
    
    # Trading behavior
    min_time_between_trades_seconds: int
    position_size_multiplier_base: float


class ProfileManager:
    """Manages trading configuration profiles"""
    
    def __init__(self, profiles_dir: str = "config/profiles"):
        """
        Initialize profile manager
        
        Args:
            profiles_dir: Directory to store profile files
        """
        self.profiles_dir = profiles_dir
        self.profiles: Dict[str, TradingProfile] = {}
        self.current_profile: Optional[TradingProfile] = None
        
        # Create profiles directory
        os.makedirs(profiles_dir, exist_ok=True)
        
        # Load default profiles
        self._create_default_profiles()
        
        # Load custom profiles
        self._load_profiles()
    
    def _create_default_profiles(self) -> None:
        """Create default trading profiles"""
        
        # Conservative profile
        conservative = TradingProfile(
            name="conservative",
            description="Low risk, steady gains",
            max_position_size_usd=50.0,
            risk_per_trade_percent=0.5,
            take_profit_percent=0.15,
            stop_loss_percent=0.10,
            spoof_threshold=0.90,
            min_spread_entry=0.05,
            max_daily_drawdown_percent=2.0,
            max_total_drawdown_percent=8.0,
            max_daily_trades=20,
            max_hourly_trades=5,
            max_consecutive_losses=3,
            min_time_between_trades_seconds=60,
            position_size_multiplier_base=0.8
        )
        
        # Balanced profile
        balanced = TradingProfile(
            name="balanced",
            description="Moderate risk, balanced approach",
            max_position_size_usd=100.0,
            risk_per_trade_percent=1.0,
            take_profit_percent=0.25,
            stop_loss_percent=0.15,
            spoof_threshold=0.85,
            min_spread_entry=0.08,
            max_daily_drawdown_percent=5.0,
            max_total_drawdown_percent=15.0,
            max_daily_trades=50,
            max_hourly_trades=10,
            max_consecutive_losses=5,
            min_time_between_trades_seconds=30,
            position_size_multiplier_base=1.0
        )
        
        # Aggressive profile
        aggressive = TradingProfile(
            name="aggressive",
            description="Higher risk, higher potential returns",
            max_position_size_usd=200.0,
            risk_per_trade_percent=2.0,
            take_profit_percent=0.40,
            stop_loss_percent=0.25,
            spoof_threshold=0.75,
            min_spread_entry=0.12,
            max_daily_drawdown_percent=8.0,
            max_total_drawdown_percent=25.0,
            max_daily_trades=100,
            max_hourly_trades=20,
            max_consecutive_losses=8,
            min_time_between_trades_seconds=15,
            position_size_multiplier_base=1.2
        )
        
        # Store default profiles
        self.profiles = {
            "conservative": conservative,
            "balanced": balanced,
            "aggressive": aggressive
        }
        
        # Save default profiles to files
        for profile in self.profiles.values():
            self._save_profile(profile)
    
    def _load_profiles(self) -> None:
        """Load profiles from files"""
        try:
            for filename in os.listdir(self.profiles_dir):
                if filename.endswith('.json'):
                    profile_name = filename[:-5]  # Remove .json extension
                    profile_path = os.path.join(self.profiles_dir, filename)
                    
                    with open(profile_path, 'r') as f:
                        profile_data = json.load(f)
                    
                    # Create profile object
                    profile = TradingProfile(**profile_data)
                    self.profiles[profile_name] = profile
                    
                    logger.info(f"Loaded profile: {profile_name}")
        
        except Exception as e:
            logger.error(f"Error loading profiles: {e}")
    
    def _save_profile(self, profile: TradingProfile) -> None:
        """
        Save profile to file
        
        Args:
            profile: Profile to save
        """
        try:
            profile_path = os.path.join(self.profiles_dir, f"{profile.name}.json")
            
            with open(profile_path, 'w') as f:
                json.dump(asdict(profile), f, indent=2)
            
            logger.info(f"Saved profile: {profile.name}")
        
        except Exception as e:
            logger.error(f"Error saving profile {profile.name}: {e}")
    
    def get_profile(self, name: str) -> Optional[TradingProfile]:
        """
        Get profile by name
        
        Args:
            name: Profile name
            
        Returns:
            Optional[TradingProfile]: Profile if found
        """
        return self.profiles.get(name)
    
    def list_profiles(self) -> Dict[str, str]:
        """
        List available profiles
        
        Returns:
            Dict[str, str]: Profile names and descriptions
        """
        return {name: profile.description for name, profile in self.profiles.items()}
    
    def set_current_profile(self, name: str) -> bool:
        """
        Set current active profile
        
        Args:
            name: Profile name
            
        Returns:
            bool: True if profile was set successfully
        """
        profile = self.get_profile(name)
        if profile:
            self.current_profile = profile
            logger.info(f"Set current profile to: {name}")
            return True
        else:
            logger.error(f"Profile not found: {name}")
            return False
    
    def get_current_profile(self) -> Optional[TradingProfile]:
        """
        Get current active profile
        
        Returns:
            Optional[TradingProfile]: Current profile
        """
        return self.current_profile
    
    def create_custom_profile(self, name: str, description: str, 
                            base_profile: str = "balanced", 
                            overrides: Optional[Dict[str, Any]] = None) -> bool:
        """
        Create a custom profile based on an existing one
        
        Args:
            name: New profile name
            description: Profile description
            base_profile: Base profile to copy from
            overrides: Settings to override
            
        Returns:
            bool: True if profile was created successfully
        """
        try:
            base = self.get_profile(base_profile)
            if not base:
                logger.error(f"Base profile not found: {base_profile}")
                return False
            
            # Create new profile from base
            profile_data = asdict(base)
            profile_data["name"] = name
            profile_data["description"] = description
            
            # Apply overrides
            if overrides:
                profile_data.update(overrides)
            
            # Create profile object
            new_profile = TradingProfile(**profile_data)
            
            # Store and save
            self.profiles[name] = new_profile
            self._save_profile(new_profile)
            
            logger.info(f"Created custom profile: {name}")
            return True
        
        except Exception as e:
            logger.error(f"Error creating custom profile {name}: {e}")
            return False
    
    def delete_profile(self, name: str) -> bool:
        """
        Delete a profile
        
        Args:
            name: Profile name
            
        Returns:
            bool: True if profile was deleted successfully
        """
        try:
            # Don't allow deletion of default profiles
            if name in ["conservative", "balanced", "aggressive"]:
                logger.error(f"Cannot delete default profile: {name}")
                return False
            
            if name not in self.profiles:
                logger.error(f"Profile not found: {name}")
                return False
            
            # Remove from memory
            del self.profiles[name]
            
            # Remove file
            profile_path = os.path.join(self.profiles_dir, f"{name}.json")
            if os.path.exists(profile_path):
                os.remove(profile_path)
            
            # Reset current profile if it was deleted
            if self.current_profile and self.current_profile.name == name:
                self.current_profile = None
            
            logger.info(f"Deleted profile: {name}")
            return True
        
        except Exception as e:
            logger.error(f"Error deleting profile {name}: {e}")
            return False
    
    def apply_profile_to_config(self, config_obj) -> None:
        """
        Apply current profile settings to config object
        
        Args:
            config_obj: Configuration object to update
        """
        if not self.current_profile:
            logger.warning("No current profile set")
            return
        
        profile = self.current_profile
        
        # Apply profile settings to config
        config_obj.max_position_size_usd = profile.max_position_size_usd
        config_obj.risk_per_trade_percent = profile.risk_per_trade_percent
        config_obj.take_profit_percent = profile.take_profit_percent
        config_obj.stop_loss_percent = profile.stop_loss_percent
        config_obj.spoof_threshold = profile.spoof_threshold
        config_obj.min_spread_entry = profile.min_spread_entry
        
        logger.info(f"Applied profile '{profile.name}' to configuration")


# Global profile manager instance
profile_manager = ProfileManager()
