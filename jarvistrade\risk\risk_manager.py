"""
Advanced risk management system for JarvisTrade Lite
"""
import logging
import json
import os
from enum import Enum
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from jarvistrade.config import config

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """Risk levels for trading"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class DrawdownControl:
    """Drawdown control configuration"""
    max_daily_drawdown_percent: float = 5.0  # Max 5% daily loss
    max_total_drawdown_percent: float = 15.0  # Max 15% total loss
    recovery_threshold_percent: float = 2.0  # Resume trading after 2% recovery
    pause_duration_minutes: int = 60  # Pause for 1 hour
    
    
@dataclass
class TradingLimits:
    """Trading limits configuration"""
    max_daily_trades: int = 50
    max_hourly_trades: int = 10
    max_consecutive_losses: int = 5
    min_time_between_trades_seconds: int = 30


class RiskManager:
    """Advanced risk management system"""
    
    def __init__(self, drawdown_control: Optional[DrawdownControl] = None,
                 trading_limits: Optional[TradingLimits] = None):
        """
        Initialize risk manager
        
        Args:
            drawdown_control: Drawdown control settings
            trading_limits: Trading limits settings
        """
        self.drawdown_control = drawdown_control or DrawdownControl()
        self.trading_limits = trading_limits or TradingLimits()
        
        # State tracking
        self.daily_pnl = 0.0
        self.total_pnl = 0.0
        self.starting_balance = 0.0
        self.current_balance = 0.0
        self.daily_trades = 0
        self.hourly_trades = 0
        self.consecutive_losses = 0
        self.last_trade_time = None
        self.trading_paused = False
        self.pause_until = None
        self.pause_reason = ""
        
        # Historical data
        self.trade_history = []
        self.daily_stats = {}
        
        # Load state if exists
        self._load_state()
    
    def update_balance(self, new_balance: float) -> None:
        """
        Update current balance
        
        Args:
            new_balance: New account balance
        """
        if self.starting_balance == 0:
            self.starting_balance = new_balance
        
        self.current_balance = new_balance
        self._save_state()
    
    def record_trade(self, trade_result: Dict[str, Any]) -> None:
        """
        Record a completed trade
        
        Args:
            trade_result: Trade result data
        """
        pnl = trade_result.get("pnl", 0.0)
        symbol = trade_result.get("symbol", "")
        timestamp = datetime.now()
        
        # Update PNL tracking
        self.daily_pnl += pnl
        self.total_pnl += pnl
        
        # Update trade counts
        self.daily_trades += 1
        self.hourly_trades += 1
        
        # Track consecutive losses
        if pnl < 0:
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0
        
        # Record trade
        trade_record = {
            "timestamp": timestamp.isoformat(),
            "symbol": symbol,
            "pnl": pnl,
            "daily_pnl": self.daily_pnl,
            "total_pnl": self.total_pnl,
            "consecutive_losses": self.consecutive_losses
        }
        
        self.trade_history.append(trade_record)
        self.last_trade_time = timestamp
        
        # Check risk conditions
        self._check_risk_conditions()
        
        # Save state
        self._save_state()
        
        logger.info(f"Trade recorded: {symbol} PNL: {pnl:.4f}, Daily PNL: {self.daily_pnl:.4f}")
    
    def can_trade(self) -> tuple[bool, str]:
        """
        Check if trading is allowed
        
        Returns:
            tuple[bool, str]: (can_trade, reason)
        """
        # Check if trading is paused
        if self.trading_paused:
            if self.pause_until and datetime.now() < self.pause_until:
                remaining = (self.pause_until - datetime.now()).total_seconds() / 60
                return False, f"Trading paused: {self.pause_reason} (Resume in {remaining:.1f} min)"
            else:
                # Check if recovery threshold is met
                if self._check_recovery_conditions():
                    self._resume_trading()
                else:
                    return False, f"Trading paused: {self.pause_reason} (Waiting for recovery)"
        
        # Check daily trade limit
        if self.daily_trades >= self.trading_limits.max_daily_trades:
            return False, f"Daily trade limit reached ({self.daily_trades}/{self.trading_limits.max_daily_trades})"
        
        # Check hourly trade limit
        if self.hourly_trades >= self.trading_limits.max_hourly_trades:
            return False, f"Hourly trade limit reached ({self.hourly_trades}/{self.trading_limits.max_hourly_trades})"
        
        # Check consecutive losses
        if self.consecutive_losses >= self.trading_limits.max_consecutive_losses:
            return False, f"Too many consecutive losses ({self.consecutive_losses})"
        
        # Check minimum time between trades
        if self.last_trade_time:
            time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
            if time_since_last < self.trading_limits.min_time_between_trades_seconds:
                remaining = self.trading_limits.min_time_between_trades_seconds - time_since_last
                return False, f"Minimum time between trades not met ({remaining:.1f}s remaining)"
        
        return True, "Trading allowed"
    
    def get_risk_level(self) -> RiskLevel:
        """
        Get current risk level
        
        Returns:
            RiskLevel: Current risk level
        """
        if self.current_balance == 0:
            return RiskLevel.LOW
        
        # Calculate drawdown percentages
        daily_drawdown = abs(self.daily_pnl / self.current_balance * 100) if self.daily_pnl < 0 else 0
        total_drawdown = abs((self.current_balance - self.starting_balance) / self.starting_balance * 100) if self.current_balance < self.starting_balance else 0
        
        # Determine risk level
        if (daily_drawdown >= self.drawdown_control.max_daily_drawdown_percent * 0.8 or 
            total_drawdown >= self.drawdown_control.max_total_drawdown_percent * 0.8 or
            self.consecutive_losses >= self.trading_limits.max_consecutive_losses - 1):
            return RiskLevel.CRITICAL
        elif (daily_drawdown >= self.drawdown_control.max_daily_drawdown_percent * 0.6 or
              total_drawdown >= self.drawdown_control.max_total_drawdown_percent * 0.6 or
              self.consecutive_losses >= self.trading_limits.max_consecutive_losses - 2):
            return RiskLevel.HIGH
        elif (daily_drawdown >= self.drawdown_control.max_daily_drawdown_percent * 0.4 or
              total_drawdown >= self.drawdown_control.max_total_drawdown_percent * 0.4):
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def get_position_size_multiplier(self) -> float:
        """
        Get position size multiplier based on risk level
        
        Returns:
            float: Multiplier for position size (0.1 to 1.0)
        """
        risk_level = self.get_risk_level()
        
        multipliers = {
            RiskLevel.LOW: 1.0,
            RiskLevel.MEDIUM: 0.7,
            RiskLevel.HIGH: 0.4,
            RiskLevel.CRITICAL: 0.1
        }
        
        return multipliers.get(risk_level, 0.5)
    
    def _check_risk_conditions(self) -> None:
        """Check if risk conditions require trading pause"""
        if self.current_balance == 0:
            return
        
        # Calculate drawdown percentages
        daily_drawdown = abs(self.daily_pnl / self.current_balance * 100) if self.daily_pnl < 0 else 0
        total_drawdown = abs((self.current_balance - self.starting_balance) / self.starting_balance * 100) if self.current_balance < self.starting_balance else 0
        
        # Check daily drawdown
        if daily_drawdown >= self.drawdown_control.max_daily_drawdown_percent:
            self._pause_trading(f"Daily drawdown limit exceeded ({daily_drawdown:.2f}%)")
            return
        
        # Check total drawdown
        if total_drawdown >= self.drawdown_control.max_total_drawdown_percent:
            self._pause_trading(f"Total drawdown limit exceeded ({total_drawdown:.2f}%)")
            return
        
        # Check consecutive losses
        if self.consecutive_losses >= self.trading_limits.max_consecutive_losses:
            self._pause_trading(f"Too many consecutive losses ({self.consecutive_losses})")
            return
    
    def _pause_trading(self, reason: str) -> None:
        """
        Pause trading
        
        Args:
            reason: Reason for pausing
        """
        self.trading_paused = True
        self.pause_until = datetime.now() + timedelta(minutes=self.drawdown_control.pause_duration_minutes)
        self.pause_reason = reason
        
        logger.warning(f"Trading paused: {reason}")
    
    def _resume_trading(self) -> None:
        """Resume trading"""
        self.trading_paused = False
        self.pause_until = None
        self.pause_reason = ""
        self.consecutive_losses = 0  # Reset consecutive losses
        
        logger.info("Trading resumed")
    
    def _check_recovery_conditions(self) -> bool:
        """
        Check if recovery conditions are met
        
        Returns:
            bool: True if recovery conditions are met
        """
        if self.current_balance == 0:
            return False
        
        # Check if we've recovered enough from daily drawdown
        if self.daily_pnl < 0:
            recovery_needed = abs(self.daily_pnl) * (self.drawdown_control.recovery_threshold_percent / 100)
            # For simplicity, we'll check if losses have reduced
            recent_trades = self.trade_history[-5:] if len(self.trade_history) >= 5 else self.trade_history
            recent_pnl = sum(trade["pnl"] for trade in recent_trades)
            
            if recent_pnl > recovery_needed:
                return True
        
        return False
    
    def reset_daily_stats(self) -> None:
        """Reset daily statistics"""
        # Save daily stats
        today = datetime.now().strftime("%Y-%m-%d")
        self.daily_stats[today] = {
            "trades": self.daily_trades,
            "pnl": self.daily_pnl,
            "max_consecutive_losses": max(self.consecutive_losses, self.daily_stats.get(today, {}).get("max_consecutive_losses", 0))
        }
        
        # Reset daily counters
        self.daily_pnl = 0.0
        self.daily_trades = 0
        self.consecutive_losses = 0
        
        # Reset hourly counter
        self.hourly_trades = 0
        
        logger.info("Daily stats reset")
        self._save_state()
    
    def reset_hourly_stats(self) -> None:
        """Reset hourly statistics"""
        self.hourly_trades = 0
        logger.debug("Hourly stats reset")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get current risk management statistics
        
        Returns:
            Dict[str, Any]: Risk management stats
        """
        risk_level = self.get_risk_level()
        can_trade, trade_reason = self.can_trade()
        
        daily_drawdown = abs(self.daily_pnl / self.current_balance * 100) if self.current_balance > 0 and self.daily_pnl < 0 else 0
        total_drawdown = abs((self.current_balance - self.starting_balance) / self.starting_balance * 100) if self.starting_balance > 0 and self.current_balance < self.starting_balance else 0
        
        return {
            "risk_level": risk_level.value,
            "can_trade": can_trade,
            "trade_reason": trade_reason,
            "daily_pnl": self.daily_pnl,
            "total_pnl": self.total_pnl,
            "daily_trades": self.daily_trades,
            "hourly_trades": self.hourly_trades,
            "consecutive_losses": self.consecutive_losses,
            "daily_drawdown_percent": daily_drawdown,
            "total_drawdown_percent": total_drawdown,
            "position_size_multiplier": self.get_position_size_multiplier(),
            "trading_paused": self.trading_paused,
            "pause_reason": self.pause_reason,
            "pause_until": self.pause_until.isoformat() if self.pause_until else None
        }
    
    def _save_state(self) -> None:
        """Save risk manager state to file"""
        try:
            os.makedirs("data", exist_ok=True)
            
            state = {
                "daily_pnl": self.daily_pnl,
                "total_pnl": self.total_pnl,
                "starting_balance": self.starting_balance,
                "current_balance": self.current_balance,
                "daily_trades": self.daily_trades,
                "hourly_trades": self.hourly_trades,
                "consecutive_losses": self.consecutive_losses,
                "last_trade_time": self.last_trade_time.isoformat() if self.last_trade_time else None,
                "trading_paused": self.trading_paused,
                "pause_until": self.pause_until.isoformat() if self.pause_until else None,
                "pause_reason": self.pause_reason,
                "daily_stats": self.daily_stats,
                "trade_history": self.trade_history[-100:]  # Keep last 100 trades
            }
            
            with open("data/risk_manager_state.json", "w") as f:
                json.dump(state, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving risk manager state: {e}")
    
    def _load_state(self) -> None:
        """Load risk manager state from file"""
        try:
            if os.path.exists("data/risk_manager_state.json"):
                with open("data/risk_manager_state.json", "r") as f:
                    state = json.load(f)
                
                self.daily_pnl = state.get("daily_pnl", 0.0)
                self.total_pnl = state.get("total_pnl", 0.0)
                self.starting_balance = state.get("starting_balance", 0.0)
                self.current_balance = state.get("current_balance", 0.0)
                self.daily_trades = state.get("daily_trades", 0)
                self.hourly_trades = state.get("hourly_trades", 0)
                self.consecutive_losses = state.get("consecutive_losses", 0)
                self.trading_paused = state.get("trading_paused", False)
                self.pause_reason = state.get("pause_reason", "")
                self.daily_stats = state.get("daily_stats", {})
                self.trade_history = state.get("trade_history", [])
                
                # Parse datetime fields
                if state.get("last_trade_time"):
                    self.last_trade_time = datetime.fromisoformat(state["last_trade_time"])
                
                if state.get("pause_until"):
                    self.pause_until = datetime.fromisoformat(state["pause_until"])
                
                logger.info("Risk manager state loaded")
                
        except Exception as e:
            logger.error(f"Error loading risk manager state: {e}")
