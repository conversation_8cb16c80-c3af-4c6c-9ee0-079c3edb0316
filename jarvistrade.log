2025-05-19 06:22:04,043 - jarvistrade.models.spoof_detector - WARNING - No model file provided or file not found
2025-05-19 06:22:05,018 - jarvistrade.api.websocket - INFO - Initialized order book for BTCUSDT
2025-05-19 06:22:05,360 - jarvistrade.api.websocket - INFO - Initialized order book for ETHUSDT
2025-05-19 06:22:05,683 - jarvistrade.api.websocket - INFO - Initialized order book for SOLUSDT
2025-05-19 06:22:35,898 - jarvistrade.models.spoof_detector - WARNING - No model file provided or file not found
2025-05-19 06:22:36,754 - jarvistrade.api.websocket - INFO - Initialized order book for BTCUSDT
2025-05-19 06:22:37,092 - jarvistrade.api.websocket - INFO - Initialized order book for ETHUSDT
2025-05-19 06:22:37,415 - jarvistrade.api.websocket - INFO - Initialized order book for SOLUSDT
2025-05-19 12:00:32,727 - jarvistrade.models.spoof_detector - WARNING - No model file provided or file not found
2025-05-19 12:00:33,686 - jarvistrade.api.websocket - INFO - Initialized order book for BTCUSDT
2025-05-19 12:00:34,034 - jarvistrade.api.websocket - INFO - Initialized order book for ETHUSDT
2025-05-19 12:00:34,883 - jarvistrade.api.websocket - INFO - Initialized order book for SOLUSDT
2025-05-19 18:19:28,515 - jarvistrade.models.spoof_detector - INFO - Loaded model from C:\Users\<USER>\Desktop\JarvisTrade\jarvistrade\models\data\spoof_detector.pkl
2025-05-19 18:19:29,363 - jarvistrade.api.websocket - INFO - Initialized order book for BTCUSDT
2025-05-19 18:19:29,693 - jarvistrade.api.websocket - INFO - Initialized order book for ETHUSDT
2025-05-19 18:19:30,555 - jarvistrade.api.websocket - INFO - Initialized order book for SOLUSDT
2025-05-19 18:19:30,555 - jarvistrade.ui.terminal - INFO - Keyboard listener started. Press 'T' to toggle trading, 'Q' to quit.
