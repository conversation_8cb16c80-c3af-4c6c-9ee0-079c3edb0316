"""
Anti-spoof trading strategy for JarvisTrade Lite
"""
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

from jarvistrade.config import config
from jarvistrade.models.spoof_detector import SpoofDetector
from jarvistrade.api.binance import BinanceClient

logger = logging.getLogger(__name__)

class AntiSpoofStrategy:
    """Trading strategy that counters spoofing behavior"""
    
    def __init__(self, spoof_detector: SpoofDetector, binance_client: BinanceClient,
                 on_signal_callback: Optional[Callable[[Dict[str, Any]], None]] = None):
        """
        Initialize anti-spoof strategy
        
        Args:
            spoof_detector: Spoof detector instance
            binance_client: Binance API client
            on_signal_callback: Callback for trading signals
        """
        self.spoof_detector = spoof_detector
        self.binance_client = binance_client
        self.on_signal_callback = on_signal_callback
        self.spoof_threshold = config.spoof_threshold
        self.min_spread_entry = config.min_spread_entry
        self.cooldown_periods = {}  # Cooldown periods for each symbol
    
    def process_order_book(self, symbol: str, order_book: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process order book and generate trading signals
        
        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            order_book: Order book data
            
        Returns:
            Optional[Dict[str, Any]]: Trading signal if generated
        """
        # Check if symbol is in cooldown
        now = datetime.now()
        if symbol in self.cooldown_periods and now < self.cooldown_periods[symbol]:
            return None
        
        # Detect spoofing
        detection_result = self.spoof_detector.detect_spoof(symbol, order_book)
        spoof_probability = detection_result["spoof_probability"]
        spoof_side = detection_result["side"]
        features = detection_result["features"]
        
        # No spoofing detected or not enough confidence
        if spoof_probability < self.spoof_threshold or spoof_side is None:
            return None
        
        # Check spread
        if "spread_percent" in features and features["spread_percent"] > self.min_spread_entry:
            logger.info(f"Spread too large for {symbol}: {features['spread_percent']}% > {self.min_spread_entry}%")
            return None
        
        # Generate trading signal
        signal = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "spoof_probability": spoof_probability,
            "spoof_side": spoof_side,
            "action": "SELL" if spoof_side == "bid" else "BUY",
            "price": features.get("best_ask" if spoof_side == "bid" else "best_bid", 0),
            "features": features
        }
        
        # Set cooldown period (5 minutes)
        self.cooldown_periods[symbol] = now.replace(minute=now.minute + 5)
        
        # Call signal callback if provided
        if self.on_signal_callback:
            self.on_signal_callback(signal)
        
        logger.info(f"Generated signal for {symbol}: {signal['action']} at {signal['price']}")
        return signal
    
    def calculate_position_size(self, symbol: str, price: float) -> float:
        """
        Calculate position size based on risk parameters
        
        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            price: Entry price
            
        Returns:
            float: Position size in base currency
        """
        # Get account balance
        try:
            account = self.binance_client.get_account()
            
            # Find USDT balance
            usdt_balance = 0
            for asset in account["balances"]:
                if asset["asset"] == "USDT":
                    usdt_balance = float(asset["free"])
                    break
            
            # Calculate position size
            risk_amount = usdt_balance * (config.risk_per_trade_percent / 100)
            
            # Limit to max position size
            position_size_usd = min(risk_amount, config.max_position_size_usd)
            
            # Convert to base currency
            position_size = position_size_usd / price
            
            return position_size
        
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0
    
    def calculate_take_profit(self, entry_price: float, side: str) -> float:
        """
        Calculate take profit price
        
        Args:
            entry_price: Entry price
            side: Order side (BUY or SELL)
            
        Returns:
            float: Take profit price
        """
        if side == "BUY":
            return entry_price * (1 + config.take_profit_percent / 100)
        else:
            return entry_price * (1 - config.take_profit_percent / 100)
    
    def calculate_stop_loss(self, entry_price: float, side: str) -> float:
        """
        Calculate stop loss price
        
        Args:
            entry_price: Entry price
            side: Order side (BUY or SELL)
            
        Returns:
            float: Stop loss price
        """
        if side == "BUY":
            return entry_price * (1 - config.stop_loss_percent / 100)
        else:
            return entry_price * (1 + config.stop_loss_percent / 100)
