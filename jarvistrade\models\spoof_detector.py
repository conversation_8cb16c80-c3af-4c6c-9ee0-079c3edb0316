"""
Spoof detection model for JarvisTrade Lite
"""
import os
import logging
import pickle
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class OrderBookFeatureExtractor:
    """Extract features from order book for spoof detection"""

    def __init__(self, window_size: int = 10):
        """
        Initialize feature extractor

        Args:
            window_size: Number of order book snapshots to keep in history
        """
        self.window_size = window_size
        self.history = {}
        self.order_lifetimes = {}

    def _calculate_order_lifetimes(self, symbol: str, side: str,
                                  current_orders: Dict[float, float]) -> Dict[float, float]:
        """
        Calculate order lifetimes

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            side: Order side (bids or asks)
            current_orders: Current orders {price: quantity}

        Returns:
            Dict[float, float]: Order lifetimes in seconds
        """
        key = f"{symbol}_{side}"
        now = datetime.now()

        # Initialize if not exists
        if key not in self.order_lifetimes:
            self.order_lifetimes[key] = {}

        # Update order lifetimes
        lifetimes = {}
        for price, qty in current_orders.items():
            # New order
            if price not in self.order_lifetimes[key]:
                self.order_lifetimes[key][price] = now
                lifetimes[price] = 0
            # Existing order
            else:
                lifetimes[price] = (now - self.order_lifetimes[key][price]).total_seconds()

        # Remove expired orders
        expired_prices = [price for price in self.order_lifetimes[key] if price not in current_orders]
        for price in expired_prices:
            del self.order_lifetimes[key][price]

        return lifetimes

    def extract_features(self, symbol: str, order_book: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract features from order book

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            order_book: Order book data

        Returns:
            Dict[str, Any]: Extracted features
        """
        # Initialize history if not exists
        if symbol not in self.history:
            self.history[symbol] = []

        # Add current order book to history
        self.history[symbol].append(order_book)

        # Keep only window_size snapshots
        if len(self.history[symbol]) > self.window_size:
            self.history[symbol].pop(0)

        # Not enough history
        if len(self.history[symbol]) < 2:
            return {}

        # Get current and previous order books
        current = self.history[symbol][-1]
        previous = self.history[symbol][-2]

        # Calculate order lifetimes
        bid_lifetimes = self._calculate_order_lifetimes(symbol, "bids", current["bids"])
        ask_lifetimes = self._calculate_order_lifetimes(symbol, "asks", current["asks"])

        # Calculate bid/ask imbalance
        bid_volume = sum(current["bids"].values())
        ask_volume = sum(current["asks"].values())
        volume_imbalance = (bid_volume - ask_volume) / (bid_volume + ask_volume) if (bid_volume + ask_volume) > 0 else 0

        # Calculate price levels
        bid_prices = sorted(current["bids"].keys(), reverse=True)
        ask_prices = sorted(current["asks"].keys())

        # Calculate spread
        best_bid = bid_prices[0] if bid_prices else 0
        best_ask = ask_prices[0] if ask_prices else 0
        spread = best_ask - best_bid if best_bid > 0 and best_ask > 0 else 0
        spread_percent = spread / best_bid * 100 if best_bid > 0 else 0

        # Calculate order book depth
        bid_depth = len(current["bids"])
        ask_depth = len(current["asks"])

        # Calculate order cancellation rate
        prev_bid_prices = set(previous["bids"].keys())
        prev_ask_prices = set(previous["asks"].keys())
        current_bid_prices = set(current["bids"].keys())
        current_ask_prices = set(current["asks"].keys())

        cancelled_bids = prev_bid_prices - current_bid_prices
        cancelled_asks = prev_ask_prices - current_ask_prices
        new_bids = current_bid_prices - prev_bid_prices
        new_asks = current_ask_prices - prev_ask_prices

        bid_cancel_rate = len(cancelled_bids) / len(prev_bid_prices) if prev_bid_prices else 0
        ask_cancel_rate = len(cancelled_asks) / len(prev_ask_prices) if prev_ask_prices else 0

        # Calculate average order lifetime
        avg_bid_lifetime = sum(bid_lifetimes.values()) / len(bid_lifetimes) if bid_lifetimes else 0
        avg_ask_lifetime = sum(ask_lifetimes.values()) / len(ask_lifetimes) if ask_lifetimes else 0

        # Calculate order size statistics
        bid_sizes = list(current["bids"].values())
        ask_sizes = list(current["asks"].values())

        avg_bid_size = sum(bid_sizes) / len(bid_sizes) if bid_sizes else 0
        avg_ask_size = sum(ask_sizes) / len(ask_sizes) if ask_sizes else 0
        max_bid_size = max(bid_sizes) if bid_sizes else 0
        max_ask_size = max(ask_sizes) if ask_sizes else 0

        # Return features
        return {
            "volume_imbalance": volume_imbalance,
            "spread": spread,
            "spread_percent": spread_percent,
            "bid_depth": bid_depth,
            "ask_depth": ask_depth,
            "bid_cancel_rate": bid_cancel_rate,
            "ask_cancel_rate": ask_cancel_rate,
            "avg_bid_lifetime": avg_bid_lifetime,
            "avg_ask_lifetime": avg_ask_lifetime,
            "avg_bid_size": avg_bid_size,
            "avg_ask_size": avg_ask_size,
            "max_bid_size": max_bid_size,
            "max_ask_size": max_ask_size,
            "bid_volume": bid_volume,
            "ask_volume": ask_volume,
            "best_bid": best_bid,
            "best_ask": best_ask
        }


class SpoofDetector:
    """Detect spoofing in order book"""

    def __init__(self, model_path: Optional[str] = None):
        """
        Initialize spoof detector

        Args:
            model_path: Path to XGBoost model file
        """
        self.model = None
        self.feature_names = None
        self.feature_extractor = OrderBookFeatureExtractor()

        # Default model path
        if model_path is None:
            model_path = os.path.join(os.path.dirname(__file__), "data", "spoof_detector.pkl")
            feature_names_path = os.path.join(os.path.dirname(__file__), "data", "feature_names.pkl")
        else:
            feature_names_path = os.path.splitext(model_path)[0] + "_feature_names.pkl"

        # Load model if exists
        if os.path.exists(model_path):
            self.load_model(model_path)

            # Load feature names if exists
            if os.path.exists(feature_names_path):
                try:
                    with open(feature_names_path, 'rb') as f:
                        self.feature_names = pickle.load(f)
                except Exception as e:
                    logger.warning(f"Failed to load feature names: {e}")
        else:
            logger.warning(f"Model file not found: {model_path}")

    def load_model(self, model_path: str) -> bool:
        """
        Load XGBoost model from file

        Args:
            model_path: Path to model file

        Returns:
            bool: True if model loaded successfully
        """
        try:
            with open(model_path, 'rb') as f:
                self.model = pickle.load(f)
            logger.info(f"Loaded model from {model_path}")
            return True
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False

    def detect_spoof(self, symbol: str, order_book: Dict[str, Any]) -> Dict[str, Any]:
        """
        Detect spoofing in order book

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            order_book: Order book data

        Returns:
            Dict[str, Any]: Spoof detection results
        """
        # Extract features
        features = self.feature_extractor.extract_features(symbol, order_book)

        # Not enough data
        if not features:
            return {"spoof_probability": 0, "side": None, "features": {}}

        # If model not loaded, use heuristic
        if self.model is None:
            return self._heuristic_detection(features)

        # Prepare features for model
        if self.feature_names:
            # Use feature names from loaded model
            feature_vector = np.array([features.get(name, 0) for name in self.feature_names]).reshape(1, -1)
        else:
            # Use default feature order
            feature_vector = np.array([
                features["volume_imbalance"],
                features["spread_percent"],
                features["bid_cancel_rate"],
                features["ask_cancel_rate"],
                features["avg_bid_lifetime"],
                features["avg_ask_lifetime"],
                features["avg_bid_size"],
                features["avg_ask_size"]
            ]).reshape(1, -1)

        # Predict
        try:
            spoof_probability = self.model.predict_proba(feature_vector)[0, 1]

            # Determine side
            side = None
            if spoof_probability > 0.5:
                if features["bid_cancel_rate"] > features["ask_cancel_rate"] and features["volume_imbalance"] > 0:
                    side = "bid"  # Spoofing on bid side
                elif features["ask_cancel_rate"] > features["bid_cancel_rate"] and features["volume_imbalance"] < 0:
                    side = "ask"  # Spoofing on ask side

            return {
                "spoof_probability": spoof_probability,
                "side": side,
                "features": features
            }

        except Exception as e:
            logger.error(f"Error predicting spoof: {e}")
            return {"spoof_probability": 0, "side": None, "features": features}

    def _heuristic_detection(self, features: Dict[str, float]) -> Dict[str, Any]:
        """
        Heuristic spoof detection when model not available

        Args:
            features: Order book features

        Returns:
            Dict[str, Any]: Spoof detection results
        """
        # Heuristic rules for spoof detection
        spoof_score = 0

        # High cancellation rate
        if features["bid_cancel_rate"] > 0.5 or features["ask_cancel_rate"] > 0.5:
            spoof_score += 0.3

        # Short order lifetime
        if features["avg_bid_lifetime"] < 5 or features["avg_ask_lifetime"] < 5:
            spoof_score += 0.3

        # Large orders
        if features["max_bid_size"] > 3 * features["avg_bid_size"] or features["max_ask_size"] > 3 * features["avg_ask_size"]:
            spoof_score += 0.2

        # Volume imbalance
        if abs(features["volume_imbalance"]) > 0.3:
            spoof_score += 0.2

        # Determine side
        side = None
        if spoof_score > 0.5:
            if features["bid_cancel_rate"] > features["ask_cancel_rate"] and features["volume_imbalance"] > 0:
                side = "bid"  # Spoofing on bid side
            elif features["ask_cancel_rate"] > features["bid_cancel_rate"] and features["volume_imbalance"] < 0:
                side = "ask"  # Spoofing on ask side

        return {
            "spoof_probability": spoof_score,
            "side": side,
            "features": features
        }
