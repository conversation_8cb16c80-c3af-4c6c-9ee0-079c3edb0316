#!/usr/bin/env python3
"""
Complete system test for JarvisTrade Lite
Tests all components with enhanced security and robustness features
"""
import os
import sys
import asyncio
import logging
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from jarvistrade.config import config
from jarvistrade.api.binance import BinanceClient
from jarvistrade.api.websocket import BinanceWebSocket, OrderBookManager
from jarvistrade.models.spoof_detector import SpoofDetector
from jarvistrade.strategies.anti_spoof import AntiSpoofStrategy
from jarvistrade.execution.order_manager import OrderManager
from jarvistrade.alerts import AlertManager, AlertType, AlertLevel
from jarvistrade.security.encryption import EncryptionManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("system_test.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
console = Console()


class SystemTester:
    """Complete system tester for JarvisTrade Lite"""
    
    def __init__(self):
        self.console = console
        self.test_results = {}
        self.alert_manager = AlertManager(log_alerts=False, save_to_file=False)
        self.alerts_received = []
        
        # Register alert callback
        for alert_type in AlertType:
            self.alert_manager.register_callback(alert_type, self._alert_callback)
    
    def _alert_callback(self, alert):
        """Callback for receiving alerts"""
        self.alerts_received.append(alert)
        self.console.print(f"[yellow]ALERT[/]: {alert.message}")
    
    async def run_all_tests(self):
        """Run all system tests"""
        self.console.print(Panel.fit(
            "[bold cyan]JarvisTrade Lite - Complete System Test[/]",
            border_style="cyan"
        ))
        
        tests = [
            ("Configuration Validation", self.test_configuration),
            ("Security Features", self.test_security),
            ("API Client", self.test_api_client),
            ("WebSocket Robustness", self.test_websocket_robustness),
            ("Spoof Detection", self.test_spoof_detection),
            ("Trading Strategy", self.test_trading_strategy),
            ("Order Management", self.test_order_management),
            ("Alert System", self.test_alert_system),
            ("Error Handling", self.test_error_handling),
            ("Integration Flow", self.test_integration_flow)
        ]
        
        for test_name, test_func in tests:
            self.console.print(f"\n[bold blue]Testing: {test_name}[/]")
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                self.test_results[test_name] = result
                status = "[green]PASS[/]" if result else "[red]FAIL[/]"
                self.console.print(f"Result: {status}")
                
            except Exception as e:
                self.test_results[test_name] = False
                self.console.print(f"Result: [red]ERROR[/] - {e}")
                logger.exception(f"Error in test {test_name}")
        
        # Print summary
        self.print_test_summary()
    
    def test_configuration(self):
        """Test configuration validation"""
        try:
            # Test valid configuration
            if not config.validate():
                self.console.print("[yellow]Warning: Configuration validation failed[/]")
                self.console.print("This is expected if API keys are not set")
                return True  # This is acceptable for testing
            
            # Test configuration properties
            assert hasattr(config, 'trading_pairs')
            assert hasattr(config, 'max_position_size_usd')
            assert hasattr(config, 'spoof_threshold')
            
            self.console.print("✓ Configuration structure is valid")
            return True
            
        except Exception as e:
            self.console.print(f"✗ Configuration test failed: {e}")
            return False
    
    def test_security(self):
        """Test security features"""
        try:
            # Test encryption manager
            test_data = "test_secret_data"
            
            # Create temporary files
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_input:
                temp_input.write(test_data)
                temp_input_path = temp_input.name
            
            temp_output_path = temp_input_path + ".enc"
            temp_decrypt_path = temp_input_path + ".dec"
            
            try:
                # Test encryption
                success = EncryptionManager.encrypt_env_file(
                    temp_input_path, temp_output_path, "test_password"
                )
                assert success, "Encryption failed"
                
                # Test decryption
                success = EncryptionManager.decrypt_env_file(
                    temp_output_path, temp_decrypt_path, "test_password"
                )
                assert success, "Decryption failed"
                
                # Verify data integrity
                with open(temp_decrypt_path, 'r') as f:
                    decrypted_data = f.read()
                assert decrypted_data == test_data, "Data integrity check failed"
                
                self.console.print("✓ Encryption/decryption working correctly")
                return True
                
            finally:
                # Cleanup
                for path in [temp_input_path, temp_output_path, temp_decrypt_path]:
                    try:
                        os.unlink(path)
                    except:
                        pass
                        
        except Exception as e:
            self.console.print(f"✗ Security test failed: {e}")
            return False
    
    def test_api_client(self):
        """Test API client functionality"""
        try:
            client = BinanceClient(testnet=True)
            
            # Test exchange info (public endpoint)
            exchange_info = client.get_exchange_info()
            assert "symbols" in exchange_info
            assert len(exchange_info["symbols"]) > 0
            
            self.console.print("✓ API client can fetch exchange info")
            
            # Test error handling
            try:
                # This should fail gracefully
                client.get_account()  # Requires authentication
            except Exception:
                pass  # Expected to fail without valid credentials
            
            self.console.print("✓ API client handles errors gracefully")
            return True
            
        except Exception as e:
            self.console.print(f"✗ API client test failed: {e}")
            return False
    
    async def test_websocket_robustness(self):
        """Test WebSocket robustness features"""
        try:
            ws_client = BinanceWebSocket(symbols=["BTCUSDT"], testnet=True)
            
            # Test heartbeat initialization
            assert hasattr(ws_client, 'last_heartbeat')
            assert hasattr(ws_client, 'heartbeat_interval')
            assert hasattr(ws_client, 'connection_timeout')
            
            self.console.print("✓ WebSocket has robustness features")
            
            # Test connection health monitoring
            assert hasattr(ws_client, 'monitor_connection_health')
            
            self.console.print("✓ WebSocket has health monitoring")
            return True
            
        except Exception as e:
            self.console.print(f"✗ WebSocket robustness test failed: {e}")
            return False
    
    def test_spoof_detection(self):
        """Test spoof detection functionality"""
        try:
            detector = SpoofDetector()
            
            # Create sample order book
            order_book = {
                "bids": {50000: 1.0, 49999: 2.0, 49998: 1.5},
                "asks": {50001: 1.0, 50002: 2.0, 50003: 1.5},
                "last_update_id": 12345
            }
            
            # Test detection
            result = detector.detect_spoof("BTCUSDT", order_book)
            
            # Verify result structure
            assert "spoof_probability" in result
            assert "side" in result
            assert "features" in result
            
            self.console.print("✓ Spoof detector returns valid results")
            return True
            
        except Exception as e:
            self.console.print(f"✗ Spoof detection test failed: {e}")
            return False
    
    def test_trading_strategy(self):
        """Test trading strategy"""
        try:
            from unittest.mock import Mock
            
            spoof_detector = Mock()
            binance_client = Mock()
            
            # Setup mock response
            spoof_detector.detect_spoof.return_value = {
                "spoof_probability": 0.9,
                "side": "bid",
                "features": {
                    "spread_percent": 0.05,
                    "best_bid": 50000,
                    "best_ask": 50001
                }
            }
            
            strategy = AntiSpoofStrategy(spoof_detector, binance_client)
            
            # Test signal generation
            order_book = {
                "bids": {50000: 1.0},
                "asks": {50001: 1.0},
                "last_update_id": 12345
            }
            
            signal = strategy.process_order_book("BTCUSDT", order_book)
            
            assert signal is not None
            assert signal["symbol"] == "BTCUSDT"
            assert signal["action"] in ["BUY", "SELL"]
            
            self.console.print("✓ Trading strategy generates valid signals")
            return True
            
        except Exception as e:
            self.console.print(f"✗ Trading strategy test failed: {e}")
            return False
    
    def test_order_management(self):
        """Test order management with security validations"""
        try:
            from unittest.mock import Mock
            
            binance_client = Mock()
            order_manager = OrderManager(binance_client, alert_manager=self.alert_manager)
            
            # Test validation methods exist
            assert hasattr(order_manager, '_validate_trading_conditions')
            assert hasattr(order_manager, '_validate_order_parameters')
            
            self.console.print("✓ Order manager has security validations")
            
            # Test alert integration
            assert order_manager.alert_manager is not None
            
            self.console.print("✓ Order manager integrated with alerts")
            return True
            
        except Exception as e:
            self.console.print(f"✗ Order management test failed: {e}")
            return False
    
    def test_alert_system(self):
        """Test alert system"""
        try:
            # Test alert creation
            self.alert_manager.alert_trade_executed("BTCUSDT", "BUY", 0.001, 50000)
            
            # Verify alert was received
            assert len(self.alerts_received) > 0
            
            last_alert = self.alerts_received[-1]
            assert last_alert.type == AlertType.TRADE_EXECUTED
            assert "BTCUSDT" in last_alert.message
            
            self.console.print("✓ Alert system working correctly")
            return True
            
        except Exception as e:
            self.console.print(f"✗ Alert system test failed: {e}")
            return False
    
    def test_error_handling(self):
        """Test error handling and recovery"""
        try:
            from unittest.mock import Mock
            
            # Test API error handling
            binance_client = Mock()
            binance_client.get_account.side_effect = Exception("API Error")
            
            order_manager = OrderManager(binance_client, alert_manager=self.alert_manager)
            
            # This should not crash
            result = order_manager._validate_trading_conditions("BTCUSDT", "BUY", 50000)
            assert result is False  # Should return False on error
            
            self.console.print("✓ Error handling working correctly")
            return True
            
        except Exception as e:
            self.console.print(f"✗ Error handling test failed: {e}")
            return False
    
    async def test_integration_flow(self):
        """Test complete integration flow"""
        try:
            # This is a simplified integration test
            # In a real scenario, this would test the complete flow
            
            # Test component initialization
            order_book_manager = OrderBookManager(symbols=["BTCUSDT"])
            spoof_detector = SpoofDetector()
            
            # Test order book processing
            order_book = order_book_manager.get_order_book("BTCUSDT")
            detection_result = spoof_detector.detect_spoof("BTCUSDT", order_book)
            
            assert "spoof_probability" in detection_result
            
            self.console.print("✓ Integration flow working")
            return True
            
        except Exception as e:
            self.console.print(f"✗ Integration flow test failed: {e}")
            return False
    
    def print_test_summary(self):
        """Print test summary"""
        table = Table(title="Test Results Summary")
        table.add_column("Test", style="cyan")
        table.add_column("Result", style="bold")
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, result in self.test_results.items():
            status = "[green]PASS[/]" if result else "[red]FAIL[/]"
            table.add_row(test_name, status)
            if result:
                passed += 1
        
        self.console.print("\n")
        self.console.print(table)
        
        # Overall result
        overall_status = "PASS" if passed == total else "FAIL"
        color = "green" if passed == total else "red"
        
        self.console.print(f"\n[bold {color}]Overall Result: {overall_status} ({passed}/{total} tests passed)[/]")
        
        if passed == total:
            self.console.print("\n[bold green]🎉 All tests passed! System is ready for use.[/]")
        else:
            self.console.print(f"\n[bold yellow]⚠️  {total - passed} test(s) failed. Please review the issues above.[/]")


async def main():
    """Main test function"""
    tester = SystemTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
