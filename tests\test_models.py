"""
Tests for the models module
"""
import unittest
from unittest.mock import patch, MagicMock

from jarvistrade.models.spoof_detector import OrderBook<PERSON>eatureExtractor, SpoofDetector


class TestOrderBookFeatureExtractor(unittest.TestCase):
    """Test cases for OrderBookFeatureExtractor"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.extractor = OrderBookFeatureExtractor()
    
    def test_extract_features_not_enough_history(self):
        """Test extract_features with not enough history"""
        # Create sample order book
        order_book = {
            "bids": {100: 1, 99: 2},
            "asks": {101: 1, 102: 2},
            "last_update_id": 1
        }
        
        # Call method
        result = self.extractor.extract_features("BTCUSDT", order_book)
        
        # Verify
        self.assertEqual(result, {})
    
    def test_extract_features(self):
        """Test extract_features"""
        # Create sample order books
        order_book1 = {
            "bids": {100: 1, 99: 2},
            "asks": {101: 1, 102: 2},
            "last_update_id": 1
        }
        
        order_book2 = {
            "bids": {100: 1, 98: 2},  # 99 removed, 98 added
            "asks": {101: 1, 103: 2},  # 102 removed, 103 added
            "last_update_id": 2
        }
        
        # Add to history
        self.extractor.extract_features("BTCUSDT", order_book1)
        result = self.extractor.extract_features("BTCUSDT", order_book2)
        
        # Verify
        self.assertIn("volume_imbalance", result)
        self.assertIn("spread", result)
        self.assertIn("bid_cancel_rate", result)
        self.assertIn("ask_cancel_rate", result)
        
        # Check specific values
        self.assertEqual(result["best_bid"], 100)
        self.assertEqual(result["best_ask"], 101)
        self.assertEqual(result["spread"], 1)


class TestSpoofDetector(unittest.TestCase):
    """Test cases for SpoofDetector"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.detector = SpoofDetector()
    
    def test_heuristic_detection(self):
        """Test heuristic detection"""
        # Create sample features
        features = {
            "volume_imbalance": 0.5,
            "spread": 1,
            "spread_percent": 0.01,
            "bid_depth": 10,
            "ask_depth": 8,
            "bid_cancel_rate": 0.6,  # High cancel rate
            "ask_cancel_rate": 0.2,
            "avg_bid_lifetime": 2,    # Short lifetime
            "avg_ask_lifetime": 10,
            "avg_bid_size": 1,
            "avg_ask_size": 1,
            "max_bid_size": 5,        # Large order
            "max_ask_size": 2,
            "bid_volume": 15,
            "ask_volume": 10,
            "best_bid": 100,
            "best_ask": 101
        }
        
        # Call method
        result = self.detector._heuristic_detection(features)
        
        # Verify
        self.assertGreater(result["spoof_probability"], 0.5)
        self.assertEqual(result["side"], "bid")
    
    def test_detect_spoof_no_features(self):
        """Test detect_spoof with no features"""
        # Create sample order book
        order_book = {
            "bids": {100: 1},
            "asks": {101: 1},
            "last_update_id": 1
        }
        
        # Call method
        result = self.detector.detect_spoof("BTCUSDT", order_book)
        
        # Verify
        self.assertEqual(result["spoof_probability"], 0)
        self.assertIsNone(result["side"])


if __name__ == "__main__":
    unittest.main()
