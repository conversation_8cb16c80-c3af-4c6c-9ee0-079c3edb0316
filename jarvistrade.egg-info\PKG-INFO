Metadata-Version: 2.1
Name: jarvistrade
Version: 0.1.0
Summary: A local trading bot for Binance
Home-page: https://github.com/jarvistrade/jarvislite
Author: JarvisTrade Team
Author-email: <EMAIL>
License: UNKNOWN
Platform: UNKNOWN
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.8
Description-Content-Type: text/markdown

# JarvisTrade Lite

A local trading bot for Binance that detects order book spoofing and executes scalp trades - all on your local machine with no server dependencies.

## Features

- **100% Local**: All processing happens on your device, no API keys sent to servers
- **Spoof Detection**: Uses XGBoost model to detect order book manipulation
- **Real-time Analysis**: Monitors order book in real-time via WebSocket
- **Secure**: API keys stored locally with ChaCha20 encryption
- **Terminal UI**: Rich-based terminal interface with live PNL tracking
- **Lightweight**: Runs on any laptop with minimal CPU usage

## How It Works

1. **Market Feed**: Connects to Binance WebSocket API to receive real-time order book data
2. **Spoof Detection**: Analyzes order book patterns to detect manipulative behavior
3. **Trading Strategy**: Enters trades against detected spoofing to capture the resulting price movement
4. **Risk Management**: Implements tight stop-loss and take-profit levels for each trade
5. **Secure Execution**: All API communication happens locally with HMAC signatures

## Installation

### Prerequisites

- Python 3.8 or higher
- Binance account with API keys (READ+TRADE permissions only)

### Install from Source

```bash
git clone https://github.com/jarvistrade/jarvislite.git
cd jarvislite
pip install -r requirements.txt
pip install -e .
```

## Usage

1. Create a `.env` file with your Binance API keys (see `.env.example`)
2. Run the bot:

```bash
python main.py
```

Or if installed as a package:

```bash
jarvistrade
```

### Terminal UI Controls

- **T**: Toggle trading on/off
- **Q**: Quit the application

## Configuration

Edit the `.env` file to configure:

- API credentials
- Trading pairs (1-3 recommended)
- Position sizing and risk parameters
- Take profit and stop loss percentages

## Security

- API keys are stored locally in an encrypted `.env` file
- Only READ+TRADE permissions are required (no withdrawal)
- All processing happens on your device
- Keys are only decrypted in memory during runtime

## Development

### Running Tests

```bash
pytest tests/
```

### Project Structure

- `jarvistrade/api/`: Binance API and WebSocket clients
- `jarvistrade/models/`: Spoof detection model
- `jarvistrade/strategies/`: Trading strategies
- `jarvistrade/execution/`: Order execution and management
- `jarvistrade/security/`: Encryption utilities
- `jarvistrade/ui/`: Terminal user interface

## License

MIT


