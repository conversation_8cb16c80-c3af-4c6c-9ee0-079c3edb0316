"""
Advanced logging configuration for JarvisTrade Lite
"""
import os
import logging
import logging.handlers
from datetime import datetime
from typing import Dict, Any
import json


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ["name", "msg", "args", "levelname", "levelno", "pathname", 
                          "filename", "module", "lineno", "funcName", "created", 
                          "msecs", "relativeCreated", "thread", "threadName", 
                          "processName", "process", "getMessage", "exc_info", 
                          "exc_text", "stack_info"]:
                log_entry[key] = value
        
        return json.dumps(log_entry)


class TradingLogFilter(logging.Filter):
    """Filter for trading-specific logs"""
    
    def filter(self, record):
        # Add trading context if available
        if hasattr(record, 'symbol'):
            record.trading_symbol = record.symbol
        if hasattr(record, 'action'):
            record.trading_action = record.action
        if hasattr(record, 'pnl'):
            record.trading_pnl = record.pnl
        
        return True


def setup_logging(log_level: str = "INFO", 
                 enable_json_logs: bool = False,
                 max_log_files: int = 10,
                 max_file_size_mb: int = 10) -> None:
    """
    Setup advanced logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        enable_json_logs: Enable JSON structured logging
        max_log_files: Maximum number of log files to keep
        max_file_size_mb: Maximum size of each log file in MB
    """
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    if enable_json_logs:
        console_formatter = JSONFormatter()
    else:
        console_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
    
    console_handler.setFormatter(console_formatter)
    console_handler.addFilter(TradingLogFilter())
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        "logs/jarvistrade.log",
        maxBytes=max_file_size_mb * 1024 * 1024,
        backupCount=max_log_files
    )
    file_handler.setLevel(logging.DEBUG)
    
    if enable_json_logs:
        file_formatter = JSONFormatter()
    else:
        file_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(lineno)d - %(message)s"
        )
    
    file_handler.setFormatter(file_formatter)
    file_handler.addFilter(TradingLogFilter())
    root_logger.addHandler(file_handler)
    
    # Trading-specific log file
    trading_handler = logging.handlers.RotatingFileHandler(
        "logs/trading.log",
        maxBytes=max_file_size_mb * 1024 * 1024,
        backupCount=max_log_files
    )
    trading_handler.setLevel(logging.INFO)
    trading_handler.setFormatter(file_formatter)
    
    # Only log trading-related messages
    class TradingOnlyFilter(logging.Filter):
        def filter(self, record):
            trading_keywords = ["trade", "order", "signal", "spoof", "pnl", "position"]
            message_lower = record.getMessage().lower()
            return any(keyword in message_lower for keyword in trading_keywords)
    
    trading_handler.addFilter(TradingOnlyFilter())
    root_logger.addHandler(trading_handler)
    
    # Error log file
    error_handler = logging.handlers.RotatingFileHandler(
        "logs/errors.log",
        maxBytes=max_file_size_mb * 1024 * 1024,
        backupCount=max_log_files
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(file_formatter)
    root_logger.addHandler(error_handler)
    
    # Performance log file
    perf_handler = logging.handlers.RotatingFileHandler(
        "logs/performance.log",
        maxBytes=max_file_size_mb * 1024 * 1024,
        backupCount=max_log_files
    )
    perf_handler.setLevel(logging.INFO)
    perf_handler.setFormatter(file_formatter)
    
    # Only log performance-related messages
    class PerformanceOnlyFilter(logging.Filter):
        def filter(self, record):
            perf_keywords = ["performance", "timer", "cache", "cpu", "memory", "latency"]
            message_lower = record.getMessage().lower()
            return any(keyword in message_lower for keyword in perf_keywords)
    
    perf_handler.addFilter(PerformanceOnlyFilter())
    root_logger.addHandler(perf_handler)
    
    logging.info("Advanced logging configuration initialized")


def get_trading_logger(name: str) -> logging.Logger:
    """
    Get a logger configured for trading operations
    
    Args:
        name: Logger name
        
    Returns:
        logging.Logger: Configured logger
    """
    logger = logging.getLogger(name)
    
    # Add trading-specific methods
    def log_trade_execution(symbol: str, action: str, quantity: float, price: float):
        logger.info(f"Trade executed: {action} {quantity} {symbol} @ {price}", 
                   extra={"symbol": symbol, "action": action, "quantity": quantity, "price": price})
    
    def log_trade_completion(symbol: str, pnl: float, exit_type: str):
        logger.info(f"Trade completed: {symbol} PNL: {pnl:.4f} ({exit_type})",
                   extra={"symbol": symbol, "pnl": pnl, "exit_type": exit_type})
    
    def log_spoof_detection(symbol: str, probability: float, side: str):
        logger.warning(f"Spoof detected: {symbol} ({side} side, {probability:.2%} confidence)",
                      extra={"symbol": symbol, "spoof_probability": probability, "spoof_side": side})
    
    # Attach methods to logger
    logger.log_trade_execution = log_trade_execution
    logger.log_trade_completion = log_trade_completion
    logger.log_spoof_detection = log_spoof_detection
    
    return logger


def get_performance_logger(name: str) -> logging.Logger:
    """
    Get a logger configured for performance monitoring
    
    Args:
        name: Logger name
        
    Returns:
        logging.Logger: Configured logger
    """
    logger = logging.getLogger(name)
    
    def log_performance_metric(metric_name: str, value: float, unit: str = ""):
        logger.info(f"Performance metric: {metric_name} = {value} {unit}",
                   extra={"metric_name": metric_name, "metric_value": value, "metric_unit": unit})
    
    def log_cache_stats(hit_rate: float, total_requests: int):
        logger.info(f"Cache performance: {hit_rate:.1f}% hit rate, {total_requests} requests",
                   extra={"cache_hit_rate": hit_rate, "cache_requests": total_requests})
    
    def log_system_resources(cpu_percent: float, memory_percent: float):
        logger.info(f"System resources: CPU {cpu_percent:.1f}%, Memory {memory_percent:.1f}%",
                   extra={"cpu_percent": cpu_percent, "memory_percent": memory_percent})
    
    # Attach methods to logger
    logger.log_performance_metric = log_performance_metric
    logger.log_cache_stats = log_cache_stats
    logger.log_system_resources = log_system_resources
    
    return logger
