"""
Performance monitoring system for JarvisTrade Lite
"""
import time
import psutil
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Performance metric data"""
    name: str
    value: float
    timestamp: float
    unit: str = ""
    category: str = "general"


@dataclass
class SystemMetrics:
    """System resource metrics"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    network_sent_mb: float
    network_recv_mb: float
    timestamp: float = field(default_factory=time.time)


class PerformanceMonitor:
    """Advanced performance monitoring system"""
    
    def __init__(self, max_history: int = 1000):
        """
        Initialize performance monitor
        
        Args:
            max_history: Maximum number of metrics to keep in history
        """
        self.max_history = max_history
        self.metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self.timers: Dict[str, float] = {}
        self.counters: Dict[str, int] = defaultdict(int)
        self.system_metrics_history: deque = deque(maxlen=max_history)
        
        # Performance thresholds
        self.thresholds = {
            "cpu_percent": 80.0,
            "memory_percent": 85.0,
            "api_response_time": 5.0,
            "websocket_latency": 1.0,
            "order_execution_time": 2.0
        }
        
        # Start system monitoring
        self._last_network_stats = psutil.net_io_counters()
        self._start_time = time.time()
    
    def start_timer(self, name: str) -> None:
        """
        Start a performance timer
        
        Args:
            name: Timer name
        """
        self.timers[name] = time.time()
    
    def end_timer(self, name: str, category: str = "timing") -> float:
        """
        End a performance timer and record the duration
        
        Args:
            name: Timer name
            category: Metric category
            
        Returns:
            float: Duration in seconds
        """
        if name not in self.timers:
            logger.warning(f"Timer {name} was not started")
            return 0.0
        
        duration = time.time() - self.timers[name]
        del self.timers[name]
        
        # Record metric
        self.record_metric(name, duration, "seconds", category)
        
        return duration
    
    def record_metric(self, name: str, value: float, unit: str = "", category: str = "general") -> None:
        """
        Record a performance metric
        
        Args:
            name: Metric name
            value: Metric value
            unit: Unit of measurement
            category: Metric category
        """
        metric = PerformanceMetric(
            name=name,
            value=value,
            timestamp=time.time(),
            unit=unit,
            category=category
        )
        
        self.metrics_history[name].append(metric)
        
        # Check thresholds
        self._check_threshold(name, value)
    
    def increment_counter(self, name: str, amount: int = 1) -> None:
        """
        Increment a counter
        
        Args:
            name: Counter name
            amount: Amount to increment
        """
        self.counters[name] += amount
    
    def record_system_metrics(self) -> SystemMetrics:
        """
        Record current system metrics
        
        Returns:
            SystemMetrics: Current system metrics
        """
        try:
            # CPU and memory
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_usage_percent = disk.used / disk.total * 100
            
            # Network stats
            network_stats = psutil.net_io_counters()
            network_sent_mb = (network_stats.bytes_sent - self._last_network_stats.bytes_sent) / 1024 / 1024
            network_recv_mb = (network_stats.bytes_recv - self._last_network_stats.bytes_recv) / 1024 / 1024
            self._last_network_stats = network_stats
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                memory_available_mb=memory.available / 1024 / 1024,
                disk_usage_percent=disk_usage_percent,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb
            )
            
            self.system_metrics_history.append(metrics)
            
            # Record individual metrics
            self.record_metric("cpu_percent", cpu_percent, "%", "system")
            self.record_metric("memory_percent", memory.percent, "%", "system")
            self.record_metric("memory_used_mb", metrics.memory_used_mb, "MB", "system")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error recording system metrics: {e}")
            return SystemMetrics(0, 0, 0, 0, 0, 0, 0)
    
    def get_metric_stats(self, name: str, duration_minutes: int = 60) -> Dict[str, float]:
        """
        Get statistics for a specific metric
        
        Args:
            name: Metric name
            duration_minutes: Duration to analyze in minutes
            
        Returns:
            Dict[str, float]: Metric statistics
        """
        if name not in self.metrics_history:
            return {}
        
        cutoff_time = time.time() - (duration_minutes * 60)
        recent_metrics = [
            m for m in self.metrics_history[name] 
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {}
        
        values = [m.value for m in recent_metrics]
        
        return {
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "avg": sum(values) / len(values),
            "latest": values[-1] if values else 0,
            "unit": recent_metrics[0].unit if recent_metrics else ""
        }
    
    def get_system_stats(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """
        Get system performance statistics
        
        Args:
            duration_minutes: Duration to analyze in minutes
            
        Returns:
            Dict[str, Any]: System statistics
        """
        cutoff_time = time.time() - (duration_minutes * 60)
        recent_metrics = [
            m for m in self.system_metrics_history 
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {}
        
        cpu_values = [m.cpu_percent for m in recent_metrics]
        memory_values = [m.memory_percent for m in recent_metrics]
        
        return {
            "cpu": {
                "avg": sum(cpu_values) / len(cpu_values),
                "max": max(cpu_values),
                "current": cpu_values[-1] if cpu_values else 0
            },
            "memory": {
                "avg": sum(memory_values) / len(memory_values),
                "max": max(memory_values),
                "current": memory_values[-1] if memory_values else 0
            },
            "uptime_hours": (time.time() - self._start_time) / 3600,
            "samples": len(recent_metrics)
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive performance summary
        
        Returns:
            Dict[str, Any]: Performance summary
        """
        summary = {
            "system": self.get_system_stats(60),
            "counters": dict(self.counters),
            "active_timers": len(self.timers),
            "total_metrics": sum(len(history) for history in self.metrics_history.values()),
            "categories": {}
        }
        
        # Group metrics by category
        categories = defaultdict(list)
        for name, history in self.metrics_history.items():
            if history:
                latest_metric = history[-1]
                categories[latest_metric.category].append({
                    "name": name,
                    "value": latest_metric.value,
                    "unit": latest_metric.unit
                })
        
        summary["categories"] = dict(categories)
        
        return summary
    
    def _check_threshold(self, name: str, value: float) -> None:
        """
        Check if metric exceeds threshold
        
        Args:
            name: Metric name
            value: Metric value
        """
        if name in self.thresholds:
            threshold = self.thresholds[name]
            if value > threshold:
                logger.warning(f"Performance threshold exceeded: {name} = {value} > {threshold}")
    
    def timer_context(self, name: str, category: str = "timing"):
        """
        Context manager for timing operations
        
        Args:
            name: Timer name
            category: Metric category
            
        Returns:
            Context manager
        """
        return TimerContext(self, name, category)


class TimerContext:
    """Context manager for performance timing"""
    
    def __init__(self, monitor: PerformanceMonitor, name: str, category: str):
        self.monitor = monitor
        self.name = name
        self.category = category
    
    def __enter__(self):
        self.monitor.start_timer(self.name)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.monitor.end_timer(self.name, self.category)


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def timed(category: str = "timing"):
    """
    Decorator for timing function execution
    
    Args:
        category: Metric category
        
    Returns:
        Decorator function
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            timer_name = f"{func.__module__}.{func.__name__}"
            
            with performance_monitor.timer_context(timer_name, category):
                return func(*args, **kwargs)
        
        return wrapper
    return decorator
