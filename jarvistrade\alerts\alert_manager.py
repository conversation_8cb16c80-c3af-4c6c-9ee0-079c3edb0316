"""
Alert management system for JarvisTrade Lite
"""
import logging
import os
import json
from enum import Enum
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class AlertType(Enum):
    """Types of alerts"""
    TRADE_EXECUTED = "trade_executed"
    TRADE_COMPLETED = "trade_completed"
    SPOOF_DETECTED = "spoof_detected"
    CONNECTION_LOST = "connection_lost"
    CONNECTION_RESTORED = "connection_restored"
    BALANCE_LOW = "balance_low"
    ERROR = "error"
    SYSTEM_START = "system_start"
    SYSTEM_STOP = "system_stop"


class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class Alert:
    """Alert data structure"""
    type: AlertType
    level: AlertLevel
    message: str
    timestamp: str
    data: Optional[Dict[str, Any]] = None


class AlertManager:
    """Manages alerts and notifications for JarvisTrade Lite"""
    
    def __init__(self, log_alerts: bool = True, save_to_file: bool = True):
        """
        Initialize alert manager
        
        Args:
            log_alerts: Whether to log alerts to console
            save_to_file: Whether to save alerts to file
        """
        self.log_alerts = log_alerts
        self.save_to_file = save_to_file
        self.alert_history: List[Alert] = []
        self.callbacks: Dict[AlertType, List[Callable[[Alert], None]]] = {}
        
        # Create alerts directory if saving to file
        if self.save_to_file:
            os.makedirs("alerts", exist_ok=True)
    
    def send_alert(self, alert_type: AlertType, level: AlertLevel, 
                   message: str, data: Optional[Dict[str, Any]] = None) -> None:
        """
        Send an alert
        
        Args:
            alert_type: Type of alert
            level: Severity level
            message: Alert message
            data: Additional data
        """
        alert = Alert(
            type=alert_type,
            level=level,
            message=message,
            timestamp=datetime.now().isoformat(),
            data=data
        )
        
        # Add to history
        self.alert_history.append(alert)
        
        # Keep only last 1000 alerts in memory
        if len(self.alert_history) > 1000:
            self.alert_history = self.alert_history[-1000:]
        
        # Log to console if enabled
        if self.log_alerts:
            self._log_alert(alert)
        
        # Save to file if enabled
        if self.save_to_file:
            self._save_alert_to_file(alert)
        
        # Call registered callbacks
        if alert_type in self.callbacks:
            for callback in self.callbacks[alert_type]:
                try:
                    callback(alert)
                except Exception as e:
                    logger.error(f"Error in alert callback: {e}")
    
    def register_callback(self, alert_type: AlertType, 
                         callback: Callable[[Alert], None]) -> None:
        """
        Register callback for specific alert type
        
        Args:
            alert_type: Type of alert to listen for
            callback: Callback function
        """
        if alert_type not in self.callbacks:
            self.callbacks[alert_type] = []
        self.callbacks[alert_type].append(callback)
    
    def _log_alert(self, alert: Alert) -> None:
        """Log alert to console"""
        level_map = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.ERROR: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL
        }
        
        log_level = level_map.get(alert.level, logging.INFO)
        logger.log(log_level, f"[{alert.type.value.upper()}] {alert.message}")
    
    def _save_alert_to_file(self, alert: Alert) -> None:
        """Save alert to file"""
        try:
            alert_file = f"alerts/alerts_{datetime.now().strftime('%Y%m%d')}.json"
            
            # Convert alert to dict
            alert_dict = {
                "type": alert.type.value,
                "level": alert.level.value,
                "message": alert.message,
                "timestamp": alert.timestamp,
                "data": alert.data
            }
            
            # Append to file
            with open(alert_file, "a") as f:
                f.write(json.dumps(alert_dict) + "\n")
                
        except Exception as e:
            logger.error(f"Error saving alert to file: {e}")
    
    def get_recent_alerts(self, count: int = 50) -> List[Alert]:
        """
        Get recent alerts
        
        Args:
            count: Number of recent alerts to return
            
        Returns:
            List[Alert]: Recent alerts
        """
        return self.alert_history[-count:]
    
    def get_alerts_by_type(self, alert_type: AlertType, count: int = 50) -> List[Alert]:
        """
        Get alerts by type
        
        Args:
            alert_type: Type of alerts to return
            count: Maximum number of alerts to return
            
        Returns:
            List[Alert]: Alerts of specified type
        """
        filtered_alerts = [alert for alert in self.alert_history if alert.type == alert_type]
        return filtered_alerts[-count:]
    
    def get_alerts_by_level(self, level: AlertLevel, count: int = 50) -> List[Alert]:
        """
        Get alerts by severity level
        
        Args:
            level: Severity level
            count: Maximum number of alerts to return
            
        Returns:
            List[Alert]: Alerts of specified level
        """
        filtered_alerts = [alert for alert in self.alert_history if alert.level == level]
        return filtered_alerts[-count:]
    
    # Convenience methods for common alerts
    def alert_trade_executed(self, symbol: str, side: str, quantity: float, price: float) -> None:
        """Alert for trade execution"""
        self.send_alert(
            AlertType.TRADE_EXECUTED,
            AlertLevel.INFO,
            f"Trade executed: {side} {quantity} {symbol} at {price}",
            {"symbol": symbol, "side": side, "quantity": quantity, "price": price}
        )
    
    def alert_trade_completed(self, symbol: str, pnl: float, exit_type: str) -> None:
        """Alert for trade completion"""
        level = AlertLevel.INFO if pnl >= 0 else AlertLevel.WARNING
        self.send_alert(
            AlertType.TRADE_COMPLETED,
            level,
            f"Trade completed: {symbol} PNL: {pnl:.4f} ({exit_type})",
            {"symbol": symbol, "pnl": pnl, "exit_type": exit_type}
        )
    
    def alert_spoof_detected(self, symbol: str, probability: float, side: str) -> None:
        """Alert for spoof detection"""
        self.send_alert(
            AlertType.SPOOF_DETECTED,
            AlertLevel.WARNING,
            f"Spoof detected: {symbol} ({side} side, {probability:.2%} confidence)",
            {"symbol": symbol, "probability": probability, "side": side}
        )
    
    def alert_connection_lost(self, connection_type: str) -> None:
        """Alert for connection loss"""
        self.send_alert(
            AlertType.CONNECTION_LOST,
            AlertLevel.ERROR,
            f"Connection lost: {connection_type}",
            {"connection_type": connection_type}
        )
    
    def alert_connection_restored(self, connection_type: str) -> None:
        """Alert for connection restoration"""
        self.send_alert(
            AlertType.CONNECTION_RESTORED,
            AlertLevel.INFO,
            f"Connection restored: {connection_type}",
            {"connection_type": connection_type}
        )
    
    def alert_balance_low(self, balance: float, threshold: float) -> None:
        """Alert for low balance"""
        self.send_alert(
            AlertType.BALANCE_LOW,
            AlertLevel.WARNING,
            f"Low balance warning: {balance:.2f} USDT (threshold: {threshold:.2f})",
            {"balance": balance, "threshold": threshold}
        )
    
    def alert_error(self, error_message: str, error_data: Optional[Dict[str, Any]] = None) -> None:
        """Alert for errors"""
        self.send_alert(
            AlertType.ERROR,
            AlertLevel.ERROR,
            f"Error: {error_message}",
            error_data
        )
    
    def alert_system_start(self) -> None:
        """Alert for system start"""
        self.send_alert(
            AlertType.SYSTEM_START,
            AlertLevel.INFO,
            "JarvisTrade Lite started successfully"
        )
    
    def alert_system_stop(self) -> None:
        """Alert for system stop"""
        self.send_alert(
            AlertType.SYSTEM_STOP,
            AlertLevel.INFO,
            "JarvisTrade Lite stopped"
        )
