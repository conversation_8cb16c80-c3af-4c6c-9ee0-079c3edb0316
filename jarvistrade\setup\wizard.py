"""
Configuration wizard for JarvisTrade Lite
"""
import os
import logging
from typing import Dict, Any, Optional
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm, IntPrompt, FloatPrompt
from rich.table import Table
from rich import box

from jarvistrade.config import config
from jarvistrade.security.encryption import EncryptionManager

logger = logging.getLogger(__name__)


class ConfigurationWizard:
    """Interactive configuration wizard for JarvisTrade Lite"""
    
    def __init__(self):
        self.console = Console()
        self.config_data = {}
    
    def run(self) -> bool:
        """
        Run the configuration wizard
        
        Returns:
            bool: True if configuration was completed successfully
        """
        try:
            self._show_welcome()
            
            if not self._collect_api_credentials():
                return False
            
            if not self._select_trading_profile():
                return False
            
            if not self._configure_trading_pairs():
                return False
            
            if not self._configure_risk_settings():
                return False
            
            if not self._review_and_save():
                return False
            
            self._show_completion()
            return True
            
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Configuration cancelled by user.[/]")
            return False
        except Exception as e:
            self.console.print(f"\n[red]Error during configuration: {e}[/]")
            logger.exception("Error in configuration wizard")
            return False
    
    def _show_welcome(self) -> None:
        """Show welcome message"""
        self.console.print(Panel.fit(
            "[bold cyan]🤖 JarvisTrade Lite Configuration Wizard[/]\n\n"
            "[white]Welcome! This wizard will help you configure JarvisTrade Lite\n"
            "for your first trading session.\n\n"
            "You'll need:\n"
            "• Binance API credentials (API Key & Secret)\n"
            "• Trading preferences\n"
            "• Risk management settings[/]",
            title="Welcome",
            border_style="cyan"
        ))
        
        if not Confirm.ask("\nReady to start configuration?", default=True):
            raise KeyboardInterrupt()
    
    def _collect_api_credentials(self) -> bool:
        """Collect Binance API credentials"""
        self.console.print("\n[bold blue]Step 1: Binance API Credentials[/]")
        self.console.print("You need API credentials from Binance to use JarvisTrade Lite.")
        self.console.print("Make sure your API key has [yellow]READ[/] and [yellow]TRADE[/] permissions only.")
        
        # Check if we should use testnet
        use_testnet = Confirm.ask("\nDo you want to use Binance Testnet for testing?", default=True)
        self.config_data["USE_TESTNET"] = "true" if use_testnet else "false"
        
        if use_testnet:
            self.console.print("\n[yellow]Using Testnet mode - Get credentials from:[/]")
            self.console.print("https://testnet.binance.vision/")
        else:
            self.console.print("\n[red]Using LIVE trading - Be careful![/]")
            self.console.print("Get credentials from: https://www.binance.com/en/my/settings/api-management")
        
        # Get API credentials
        api_key = Prompt.ask("\nEnter your Binance API Key")
        if not api_key:
            self.console.print("[red]API Key is required![/]")
            return False
        
        api_secret = Prompt.ask("Enter your Binance API Secret", password=True)
        if not api_secret:
            self.console.print("[red]API Secret is required![/]")
            return False
        
        self.config_data["BINANCE_API_KEY"] = api_key
        self.config_data["BINANCE_API_SECRET"] = api_secret
        
        self.console.print("[green]✓ API credentials configured[/]")
        return True
    
    def _select_trading_profile(self) -> bool:
        """Select trading profile"""
        self.console.print("\n[bold blue]Step 2: Trading Profile[/]")
        self.console.print("Choose a trading profile that matches your risk tolerance:")
        
        # Show profile options
        table = Table(box=box.ROUNDED)
        table.add_column("Profile", style="cyan")
        table.add_column("Risk Level", justify="center")
        table.add_column("Max Position", justify="right")
        table.add_column("Daily Trades", justify="right")
        table.add_column("Description")
        
        profiles = {
            "1": ("Conservative", "LOW", "$50", "20", "Low risk, steady gains"),
            "2": ("Balanced", "MEDIUM", "$100", "50", "Moderate risk, balanced approach"),
            "3": ("Aggressive", "HIGH", "$200", "100", "Higher risk, higher potential returns"),
            "4": ("Custom", "CUSTOM", "Custom", "Custom", "Configure your own settings")
        }
        
        for key, (name, risk, position, trades, desc) in profiles.items():
            risk_color = {"LOW": "green", "MEDIUM": "yellow", "HIGH": "red", "CUSTOM": "cyan"}[risk]
            table.add_row(
                f"{key}. {name}",
                f"[{risk_color}]{risk}[/]",
                position,
                trades,
                desc
            )
        
        self.console.print(table)
        
        choice = Prompt.ask("\nSelect profile", choices=["1", "2", "3", "4"], default="2")
        
        profile_map = {
            "1": "conservative",
            "2": "balanced", 
            "3": "aggressive",
            "4": "custom"
        }
        
        selected_profile = profile_map[choice]
        self.config_data["TRADING_PROFILE"] = selected_profile
        
        if selected_profile == "custom":
            return self._configure_custom_profile()
        
        self.console.print(f"[green]✓ Selected {profiles[choice][0]} profile[/]")
        return True
    
    def _configure_custom_profile(self) -> bool:
        """Configure custom trading profile"""
        self.console.print("\n[bold yellow]Custom Profile Configuration[/]")
        
        # Max position size
        max_position = FloatPrompt.ask(
            "Maximum position size (USD)",
            default=100.0,
            show_default=True
        )
        self.config_data["MAX_POSITION_SIZE_USD"] = str(max_position)
        
        # Risk per trade
        risk_percent = FloatPrompt.ask(
            "Risk per trade (%)",
            default=1.0,
            show_default=True
        )
        self.config_data["RISK_PER_TRADE_PERCENT"] = str(risk_percent)
        
        # Daily trades limit
        daily_trades = IntPrompt.ask(
            "Maximum daily trades",
            default=50,
            show_default=True
        )
        self.config_data["MAX_DAILY_TRADES"] = str(daily_trades)
        
        # Spoof threshold
        spoof_threshold = FloatPrompt.ask(
            "Spoof detection threshold (0.0-1.0)",
            default=0.8,
            show_default=True
        )
        self.config_data["SPOOF_THRESHOLD"] = str(spoof_threshold)
        
        self.console.print("[green]✓ Custom profile configured[/]")
        return True
    
    def _configure_trading_pairs(self) -> bool:
        """Configure trading pairs"""
        self.console.print("\n[bold blue]Step 3: Trading Pairs[/]")
        self.console.print("Select which cryptocurrency pairs you want to trade:")
        
        # Popular pairs
        popular_pairs = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "SOLUSDT"]
        
        self.console.print("\nPopular pairs:")
        for i, pair in enumerate(popular_pairs, 1):
            self.console.print(f"{i}. {pair}")
        
        # Let user select pairs
        selected_pairs = []
        
        if Confirm.ask("\nUse default pair (BTCUSDT only)?", default=True):
            selected_pairs = ["BTCUSDT"]
        else:
            pair_choices = Prompt.ask(
                "Enter pair numbers (comma-separated, e.g., 1,2,3)",
                default="1"
            )
            
            try:
                indices = [int(x.strip()) - 1 for x in pair_choices.split(",")]
                selected_pairs = [popular_pairs[i] for i in indices if 0 <= i < len(popular_pairs)]
            except (ValueError, IndexError):
                self.console.print("[yellow]Invalid selection, using BTCUSDT[/]")
                selected_pairs = ["BTCUSDT"]
        
        self.config_data["TRADING_PAIRS"] = ",".join(selected_pairs)
        self.console.print(f"[green]✓ Selected pairs: {', '.join(selected_pairs)}[/]")
        return True
    
    def _configure_risk_settings(self) -> bool:
        """Configure risk management settings"""
        self.console.print("\n[bold blue]Step 4: Risk Management[/]")
        self.console.print("Configure additional risk management settings:")
        
        # Stop loss
        stop_loss = FloatPrompt.ask(
            "Stop loss percentage",
            default=2.0,
            show_default=True
        )
        self.config_data["STOP_LOSS_PERCENT"] = str(stop_loss)
        
        # Take profit
        take_profit = FloatPrompt.ask(
            "Take profit percentage", 
            default=3.0,
            show_default=True
        )
        self.config_data["TAKE_PROFIT_PERCENT"] = str(take_profit)
        
        # Daily drawdown limit
        daily_drawdown = FloatPrompt.ask(
            "Maximum daily drawdown (%)",
            default=5.0,
            show_default=True
        )
        self.config_data["MAX_DAILY_DRAWDOWN_PERCENT"] = str(daily_drawdown)
        
        self.console.print("[green]✓ Risk settings configured[/]")
        return True
    
    def _review_and_save(self) -> bool:
        """Review configuration and save"""
        self.console.print("\n[bold blue]Step 5: Review & Save[/]")
        
        # Show configuration summary
        table = Table(title="Configuration Summary", box=box.ROUNDED)
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="white")
        
        display_config = {
            "Trading Mode": "Testnet" if self.config_data.get("USE_TESTNET") == "true" else "Live Trading",
            "API Key": self.config_data.get("BINANCE_API_KEY", "")[:8] + "...",
            "Trading Profile": self.config_data.get("TRADING_PROFILE", "balanced").title(),
            "Trading Pairs": self.config_data.get("TRADING_PAIRS", "BTCUSDT"),
            "Stop Loss": f"{self.config_data.get('STOP_LOSS_PERCENT', '2.0')}%",
            "Take Profit": f"{self.config_data.get('TAKE_PROFIT_PERCENT', '3.0')}%",
            "Daily Drawdown Limit": f"{self.config_data.get('MAX_DAILY_DRAWDOWN_PERCENT', '5.0')}%"
        }
        
        for setting, value in display_config.items():
            table.add_row(setting, value)
        
        self.console.print(table)
        
        if not Confirm.ask("\nSave this configuration?", default=True):
            return False
        
        # Save configuration
        return self._save_configuration()
    
    def _save_configuration(self) -> bool:
        """Save configuration to .env file"""
        try:
            # Create .env content
            env_content = []
            for key, value in self.config_data.items():
                env_content.append(f"{key}={value}")
            
            # Write .env file
            with open(".env", "w") as f:
                f.write("\n".join(env_content))
            
            # Ask about encryption
            if Confirm.ask("\nEncrypt configuration file for security?", default=True):
                password = Prompt.ask("Enter encryption password", password=True)
                password_confirm = Prompt.ask("Confirm password", password=True)
                
                if password != password_confirm:
                    self.console.print("[red]Passwords don't match. Configuration saved unencrypted.[/]")
                    return True
                
                if EncryptionManager.encrypt_env_file(".env", ".env.enc", password):
                    os.remove(".env")
                    self.console.print("[green]✓ Configuration encrypted and saved[/]")
                else:
                    self.console.print("[yellow]Encryption failed. Configuration saved unencrypted.[/]")
            
            return True
            
        except Exception as e:
            self.console.print(f"[red]Error saving configuration: {e}[/]")
            return False
    
    def _show_completion(self) -> None:
        """Show completion message"""
        self.console.print(Panel.fit(
            "[bold green]🎉 Configuration Complete![/]\n\n"
            "[white]JarvisTrade Lite is now configured and ready to use.\n\n"
            "Next steps:\n"
            "• Run [cyan]python main.py[/] to start trading\n"
            "• Press [yellow]T[/] to toggle trading on/off\n"
            "• Press [yellow]Q[/] to quit\n\n"
            "Happy trading! 🚀[/]",
            title="Setup Complete",
            border_style="green"
        ))


# Global wizard instance
setup_wizard = ConfigurationWizard()
