#!/usr/bin/env python3
"""
Complete setup script for JarvisTrade Lite
Handles installation, configuration, and first-time setup
"""
import os
import sys
import subprocess
import logging
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

console = Console()


class JarvisTradeSetup:
    """Complete setup manager for JarvisTrade Lite"""
    
    def __init__(self):
        self.console = console
        self.project_root = Path(__file__).parent
    
    def run_complete_setup(self) -> bool:
        """
        Run complete setup process
        
        Returns:
            bool: True if setup completed successfully
        """
        try:
            self._show_welcome()
            
            if not self._check_python_version():
                return False
            
            if not self._install_dependencies():
                return False
            
            if not self._create_directories():
                return False
            
            if not self._setup_logging():
                return False
            
            if not self._run_configuration_wizard():
                return False
            
            if not self._verify_installation():
                return False
            
            self._show_completion()
            return True
            
        except KeyboardInterrupt:
            console.print("\n[yellow]Setup cancelled by user.[/]")
            return False
        except Exception as e:
            console.print(f"\n[red]Setup failed: {e}[/]")
            logging.exception("Setup failed")
            return False
    
    def _show_welcome(self) -> None:
        """Show welcome message"""
        console.print(Panel.fit(
            "[bold cyan]🤖 JarvisTrade Lite Setup[/]\n\n"
            "[white]Welcome to JarvisTrade Lite!\n\n"
            "This setup will:\n"
            "• Install required dependencies\n"
            "• Create necessary directories\n"
            "• Configure logging\n"
            "• Run configuration wizard\n"
            "• Verify installation\n\n"
            "Let's get started! 🚀[/]",
            title="Welcome to JarvisTrade Lite",
            border_style="cyan"
        ))
        
        if not Confirm.ask("Continue with setup?", default=True):
            raise KeyboardInterrupt()
    
    def _check_python_version(self) -> bool:
        """Check Python version compatibility"""
        console.print("\n[bold blue]Checking Python version...[/]")
        
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            console.print(f"[red]Error: Python 3.8+ required, found {version.major}.{version.minor}[/]")
            return False
        
        console.print(f"[green]✓ Python {version.major}.{version.minor}.{version.micro} detected[/]")
        return True
    
    def _install_dependencies(self) -> bool:
        """Install required dependencies"""
        console.print("\n[bold blue]Installing dependencies...[/]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Installing packages...", total=None)
            
            try:
                # Install requirements
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
                ], capture_output=True, text=True, check=True)
                
                progress.update(task, description="✓ Dependencies installed")
                console.print("[green]✓ All dependencies installed successfully[/]")
                return True
                
            except subprocess.CalledProcessError as e:
                progress.update(task, description="✗ Installation failed")
                console.print(f"[red]Error installing dependencies: {e.stderr}[/]")
                return False
            except FileNotFoundError:
                progress.update(task, description="✗ requirements.txt not found")
                console.print("[red]Error: requirements.txt not found[/]")
                return False
    
    def _create_directories(self) -> bool:
        """Create necessary directories"""
        console.print("\n[bold blue]Creating directories...[/]")
        
        directories = [
            "data",
            "logs", 
            "backups",
            "alerts",
            "config/profiles"
        ]
        
        try:
            for directory in directories:
                dir_path = Path(directory)
                dir_path.mkdir(parents=True, exist_ok=True)
                console.print(f"[green]✓ Created {directory}/[/]")
            
            return True
            
        except Exception as e:
            console.print(f"[red]Error creating directories: {e}[/]")
            return False
    
    def _setup_logging(self) -> bool:
        """Setup logging configuration"""
        console.print("\n[bold blue]Setting up logging...[/]")
        
        try:
            from jarvistrade.utils.logging_config import setup_logging
            
            setup_logging(
                log_level="INFO",
                enable_json_logs=False,
                max_log_files=10,
                max_file_size_mb=10
            )
            
            console.print("[green]✓ Logging configured[/]")
            return True
            
        except Exception as e:
            console.print(f"[red]Error setting up logging: {e}[/]")
            return False
    
    def _run_configuration_wizard(self) -> bool:
        """Run configuration wizard"""
        console.print("\n[bold blue]Running configuration wizard...[/]")
        
        try:
            from jarvistrade.setup.wizard import setup_wizard
            
            if setup_wizard.run():
                console.print("[green]✓ Configuration completed[/]")
                return True
            else:
                console.print("[red]Configuration wizard failed[/]")
                return False
                
        except Exception as e:
            console.print(f"[red]Error in configuration wizard: {e}[/]")
            return False
    
    def _verify_installation(self) -> bool:
        """Verify installation by running tests"""
        console.print("\n[bold blue]Verifying installation...[/]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Running verification tests...", total=None)
            
            try:
                # Import main modules to verify they work
                from jarvistrade.config import config
                from jarvistrade.api.binance import BinanceClient
                from jarvistrade.models.spoof_detector import SpoofDetector
                from jarvistrade.alerts import AlertManager
                from jarvistrade.risk import RiskManager
                
                progress.update(task, description="✓ Core modules verified")
                
                # Test configuration loading
                if config.validate():
                    progress.update(task, description="✓ Configuration valid")
                else:
                    progress.update(task, description="⚠ Configuration needs API keys")
                
                console.print("[green]✓ Installation verified successfully[/]")
                return True
                
            except Exception as e:
                progress.update(task, description="✗ Verification failed")
                console.print(f"[red]Verification failed: {e}[/]")
                return False
    
    def _show_completion(self) -> None:
        """Show completion message"""
        console.print(Panel.fit(
            "[bold green]🎉 Setup Complete![/]\n\n"
            "[white]JarvisTrade Lite has been successfully installed and configured!\n\n"
            "What's next:\n"
            "• Run [cyan]python main.py[/] to start the trading bot\n"
            "• Use [cyan]python test_system_complete.py[/] to run system tests\n"
            "• Check [cyan]logs/[/] directory for detailed logs\n"
            "• Use [cyan]python -c \"from jarvistrade.setup.wizard import setup_wizard; setup_wizard.run()\"[/] to reconfigure\n\n"
            "Documentation:\n"
            "• README.md - Complete documentation\n"
            "• checklist.md - Development status\n\n"
            "Happy trading! 🚀[/]",
            title="Setup Complete",
            border_style="green"
        ))
        
        console.print("\n[bold cyan]Quick Start Commands:[/]")
        console.print("  [cyan]python main.py[/]                    # Start trading")
        console.print("  [cyan]python test_system_complete.py[/]    # Run tests")
        console.print("  [cyan]python setup_jarvistrade.py[/]       # Re-run setup")


def main():
    """Main setup function"""
    setup = JarvisTradeSetup()
    
    if setup.run_complete_setup():
        console.print("\n[bold green]Setup completed successfully! 🎉[/]")
        sys.exit(0)
    else:
        console.print("\n[bold red]Setup failed. Please check the errors above.[/]")
        sys.exit(1)


if __name__ == "__main__":
    main()
