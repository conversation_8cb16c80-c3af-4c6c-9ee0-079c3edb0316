2025-05-23 17:52:59,241 - jarvistrade.api.binance - ERROR - API request error: 400 Client Error: Bad Request for url: https://testnet.binance.vision/api/v3/account?timestamp=*************&signature=196b32d5df1a6b16779a0767b8ee87a82293ad98e029f424e187010295de891b
2025-05-23 17:52:59,242 - jarvistrade.api.binance - ERROR - Response: {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-05-23 17:52:59,242 - jarvistrade.api.binance - INFO - Retrying in 1.00 seconds (attempt 1/3)
2025-05-23 17:53:00,589 - jarvistrade.api.binance - ERROR - API request error: 401 Client Error: Unauthorized for url: https://testnet.binance.vision/api/v3/account?timestamp=*************&signature=196b32d5df1a6b16779a0767b8ee87a82293ad98e029f424e187010295de891b
2025-05-23 17:53:00,641 - jarvistrade.api.binance - ERROR - Response: {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}
2025-05-23 17:53:00,659 - jarvistrade.api.binance - INFO - Retrying in 2.00 seconds (attempt 2/3)
2025-05-23 17:53:03,038 - jarvistrade.api.binance - ERROR - API request error: 401 Client Error: Unauthorized for url: https://testnet.binance.vision/api/v3/account?timestamp=*************&signature=196b32d5df1a6b16779a0767b8ee87a82293ad98e029f424e187010295de891b
2025-05-23 17:53:03,039 - jarvistrade.api.binance - ERROR - Response: {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}
2025-05-23 17:53:03,039 - jarvistrade.api.binance - INFO - Retrying in 4.00 seconds (attempt 3/3)
2025-05-23 17:53:07,384 - jarvistrade.api.binance - ERROR - API request error: 400 Client Error: Bad Request for url: https://testnet.binance.vision/api/v3/account?timestamp=*************&signature=196b32d5df1a6b16779a0767b8ee87a82293ad98e029f424e187010295de891b
2025-05-23 17:53:07,385 - jarvistrade.api.binance - ERROR - Response: {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-05-23 17:53:07,386 - jarvistrade.api.binance - ERROR - Max retries reached for /v3/account
2025-05-23 17:53:20,119 - jarvistrade.models.spoof_detector - INFO - Loaded model from C:\Users\<USER>\Desktop\JarvisTrade\jarvistrade\models\data\spoof_detector.pkl
2025-05-23 17:53:20,154 - jarvistrade.strategies.anti_spoof - INFO - Generated signal for BTCUSDT: SELL at 50001
2025-05-23 17:53:20,169 - jarvistrade.execution.order_manager - ERROR - Error validating trading conditions: API Error
2025-05-23 17:53:20,175 - jarvistrade.models.spoof_detector - INFO - Loaded model from C:\Users\<USER>\Desktop\JarvisTrade\jarvistrade\models\data\spoof_detector.pkl
