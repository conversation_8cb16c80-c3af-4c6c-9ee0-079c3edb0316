"""
Backup and recovery system for JarvisTrade Lite
"""
import os
import json
import shutil
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class BackupManager:
    """Manages backups of configuration, data, and logs"""
    
    def __init__(self, backup_dir: str = "backups"):
        """
        Initialize backup manager
        
        Args:
            backup_dir: Directory to store backups
        """
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
    
    def create_full_backup(self) -> Optional[str]:
        """
        Create a full backup of all important files
        
        Returns:
            Optional[str]: Backup directory path if successful
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = self.backup_dir / f"backup_{timestamp}"
            backup_path.mkdir(exist_ok=True)
            
            # Files and directories to backup
            backup_items = [
                (".env", "config/.env"),
                (".env.enc", "config/.env.enc"),
                ("data/", "data/"),
                ("alerts/", "alerts/"),
                ("config/profiles/", "config/profiles/"),
                ("jarvistrade.log", "logs/jarvistrade.log"),
                ("trades.csv", "data/trades.csv")
            ]
            
            backed_up_items = []
            
            for source, dest in backup_items:
                source_path = Path(source)
                dest_path = backup_path / dest
                
                if source_path.exists():
                    dest_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    if source_path.is_file():
                        shutil.copy2(source_path, dest_path)
                        backed_up_items.append(source)
                    elif source_path.is_dir():
                        shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
                        backed_up_items.append(source)
            
            # Create backup manifest
            manifest = {
                "timestamp": timestamp,
                "created_at": datetime.now().isoformat(),
                "items": backed_up_items,
                "version": "1.0"
            }
            
            with open(backup_path / "manifest.json", "w") as f:
                json.dump(manifest, f, indent=2)
            
            logger.info(f"Full backup created: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return None
    
    def create_config_backup(self) -> Optional[str]:
        """
        Create backup of configuration files only
        
        Returns:
            Optional[str]: Backup file path if successful
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"config_backup_{timestamp}.json"
            
            config_data = {}
            
            # Backup .env file (without sensitive data)
            if os.path.exists(".env"):
                with open(".env", "r") as f:
                    env_lines = f.readlines()
                
                safe_config = {}
                for line in env_lines:
                    if "=" in line and not line.startswith("#"):
                        key, value = line.strip().split("=", 1)
                        # Don't backup sensitive keys
                        if "SECRET" not in key and "PASSWORD" not in key:
                            safe_config[key] = value
                
                config_data["env_config"] = safe_config
            
            # Backup profiles
            profiles_dir = Path("config/profiles")
            if profiles_dir.exists():
                profiles = {}
                for profile_file in profiles_dir.glob("*.json"):
                    with open(profile_file, "r") as f:
                        profiles[profile_file.stem] = json.load(f)
                config_data["profiles"] = profiles
            
            # Save backup
            with open(backup_file, "w") as f:
                json.dump(config_data, f, indent=2)
            
            logger.info(f"Configuration backup created: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            logger.error(f"Error creating config backup: {e}")
            return None
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """
        List available backups
        
        Returns:
            List[Dict[str, Any]]: List of backup information
        """
        backups = []
        
        try:
            # Full backups
            for backup_dir in self.backup_dir.glob("backup_*"):
                if backup_dir.is_dir():
                    manifest_file = backup_dir / "manifest.json"
                    if manifest_file.exists():
                        with open(manifest_file, "r") as f:
                            manifest = json.load(f)
                        
                        backups.append({
                            "type": "full",
                            "path": str(backup_dir),
                            "timestamp": manifest.get("timestamp"),
                            "created_at": manifest.get("created_at"),
                            "items": manifest.get("items", []),
                            "size": self._get_directory_size(backup_dir)
                        })
            
            # Config backups
            for backup_file in self.backup_dir.glob("config_backup_*.json"):
                stat = backup_file.stat()
                timestamp = backup_file.stem.replace("config_backup_", "")
                
                backups.append({
                    "type": "config",
                    "path": str(backup_file),
                    "timestamp": timestamp,
                    "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "size": stat.st_size
                })
            
            # Sort by creation time (newest first)
            backups.sort(key=lambda x: x["created_at"], reverse=True)
            
        except Exception as e:
            logger.error(f"Error listing backups: {e}")
        
        return backups
    
    def restore_backup(self, backup_path: str) -> bool:
        """
        Restore from backup
        
        Args:
            backup_path: Path to backup to restore
            
        Returns:
            bool: True if restore was successful
        """
        try:
            backup_path = Path(backup_path)
            
            if not backup_path.exists():
                logger.error(f"Backup path does not exist: {backup_path}")
                return False
            
            if backup_path.is_dir():
                return self._restore_full_backup(backup_path)
            elif backup_path.suffix == ".json":
                return self._restore_config_backup(backup_path)
            else:
                logger.error(f"Unknown backup type: {backup_path}")
                return False
                
        except Exception as e:
            logger.error(f"Error restoring backup: {e}")
            return False
    
    def _restore_full_backup(self, backup_path: Path) -> bool:
        """Restore full backup"""
        try:
            manifest_file = backup_path / "manifest.json"
            if not manifest_file.exists():
                logger.error("Backup manifest not found")
                return False
            
            with open(manifest_file, "r") as f:
                manifest = json.load(f)
            
            # Restore each item
            for item in manifest.get("items", []):
                source_path = backup_path / "data" / item if item.startswith("data/") else backup_path / item
                dest_path = Path(item)
                
                if source_path.exists():
                    if dest_path.exists():
                        # Create backup of existing file
                        backup_name = f"{dest_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        shutil.move(str(dest_path), backup_name)
                    
                    dest_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    if source_path.is_file():
                        shutil.copy2(source_path, dest_path)
                    elif source_path.is_dir():
                        shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
            
            logger.info(f"Full backup restored from: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error restoring full backup: {e}")
            return False
    
    def _restore_config_backup(self, backup_path: Path) -> bool:
        """Restore configuration backup"""
        try:
            with open(backup_path, "r") as f:
                config_data = json.load(f)
            
            # Restore env config
            if "env_config" in config_data:
                env_lines = []
                for key, value in config_data["env_config"].items():
                    env_lines.append(f"{key}={value}")
                
                # Backup existing .env
                if os.path.exists(".env"):
                    backup_name = f".env.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    shutil.copy2(".env", backup_name)
                
                with open(".env", "w") as f:
                    f.write("\n".join(env_lines))
            
            # Restore profiles
            if "profiles" in config_data:
                profiles_dir = Path("config/profiles")
                profiles_dir.mkdir(parents=True, exist_ok=True)
                
                for profile_name, profile_data in config_data["profiles"].items():
                    profile_file = profiles_dir / f"{profile_name}.json"
                    with open(profile_file, "w") as f:
                        json.dump(profile_data, f, indent=2)
            
            logger.info(f"Configuration restored from: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error restoring config backup: {e}")
            return False
    
    def cleanup_old_backups(self, keep_count: int = 10) -> int:
        """
        Clean up old backups, keeping only the most recent ones
        
        Args:
            keep_count: Number of backups to keep
            
        Returns:
            int: Number of backups removed
        """
        try:
            backups = self.list_backups()
            
            if len(backups) <= keep_count:
                return 0
            
            # Remove oldest backups
            to_remove = backups[keep_count:]
            removed_count = 0
            
            for backup in to_remove:
                backup_path = Path(backup["path"])
                try:
                    if backup_path.is_dir():
                        shutil.rmtree(backup_path)
                    else:
                        backup_path.unlink()
                    removed_count += 1
                except Exception as e:
                    logger.error(f"Error removing backup {backup_path}: {e}")
            
            logger.info(f"Cleaned up {removed_count} old backups")
            return removed_count
            
        except Exception as e:
            logger.error(f"Error cleaning up backups: {e}")
            return 0
    
    def _get_directory_size(self, path: Path) -> int:
        """Get total size of directory in bytes"""
        total_size = 0
        try:
            for file_path in path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception as e:
            logger.error(f"Error calculating directory size: {e}")
        return total_size


# Global backup manager instance
backup_manager = BackupManager()
