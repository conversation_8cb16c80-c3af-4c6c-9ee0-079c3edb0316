"""
WebSocket client for Binance real-time data
"""
import json
import asyncio
import logging
from typing import Dict, List, Any, Callable, Optional, Set
import websockets
from websockets.exceptions import ConnectionClosed

from jarvistrade.config import config
from jarvistrade.api.binance import BinanceClient

logger = logging.getLogger(__name__)

class BinanceWebSocket:
    """WebSocket client for Binance real-time data"""

    def __init__(self, symbols: Optional[List[str]] = None, testnet: bool = True):
        """
        Initialize WebSocket client

        Args:
            symbols: List of trading pair symbols (e.g., ["BTCUSDT", "ETHUSDT"])
            testnet: Use testnet if True
        """
        self.symbols = symbols or config.trading_pairs
        self.testnet = testnet or config.use_testnet
        self.binance_client = BinanceClient(testnet=self.testnet)
        self.ws_url = self.binance_client.ws_url
        self.connections = {}
        self.callbacks = {}
        self.running = False
        self.reconnect_delay = 1  # Initial reconnect delay in seconds
        self.max_reconnect_delay = 60  # Maximum reconnect delay in seconds
        self.last_heartbeat = {}  # Track last heartbeat for each connection
        self.heartbeat_interval = 30  # Heartbeat check interval in seconds
        self.connection_timeout = 10  # Connection timeout in seconds

    async def _connect_to_stream(self, stream: str, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        Connect to a WebSocket stream

        Args:
            stream: Stream name
            callback: Callback function for stream messages
        """
        url = f"{self.ws_url}/{stream}"

        while self.running:
            try:
                logger.info(f"Connecting to {url}")

                # Set connection timeout
                connection_timeout = 30  # 30 seconds timeout

                # Connect with timeout
                try:
                    websocket = await asyncio.wait_for(
                        websockets.connect(url),
                        timeout=connection_timeout
                    )
                    self.connections[stream] = websocket
                    self.reconnect_delay = 1  # Reset reconnect delay on successful connection
                    logger.info(f"Connected to {stream}")

                    # Send ping every 30 seconds to keep connection alive
                    ping_task = asyncio.create_task(self._ping_websocket(websocket, stream))

                    try:
                        while self.running:
                            try:
                                # Set receive timeout
                                message = await asyncio.wait_for(
                                    websocket.recv(),
                                    timeout=60  # 60 seconds timeout
                                )
                                data = json.loads(message)

                                # Update heartbeat
                                self.last_heartbeat[stream] = asyncio.get_event_loop().time()

                                await callback(data)
                            except asyncio.TimeoutError:
                                logger.warning(f"Receive timeout for {stream}, sending ping")
                                await websocket.ping()
                            except ConnectionClosed as e:
                                logger.warning(f"Connection closed for {stream}: {e}")
                                break
                    finally:
                        # Cancel ping task
                        ping_task.cancel()
                        try:
                            await ping_task
                        except asyncio.CancelledError:
                            pass

                        # Close websocket
                        try:
                            await websocket.close()
                        except Exception as e:
                            logger.error(f"Error closing websocket for {stream}: {e}")

                except asyncio.TimeoutError:
                    logger.error(f"Connection timeout for {stream}")

            except Exception as e:
                logger.error(f"WebSocket error for {stream}: {e}")

            if self.running:
                logger.info(f"Reconnecting to {stream} in {self.reconnect_delay} seconds")
                await asyncio.sleep(self.reconnect_delay)
                # Exponential backoff for reconnect
                self.reconnect_delay = min(self.reconnect_delay * 2, self.max_reconnect_delay)

    async def _ping_websocket(self, websocket, stream: str) -> None:
        """
        Send periodic pings to keep WebSocket connection alive

        Args:
            websocket: WebSocket connection
            stream: Stream name
        """
        try:
            while True:
                await asyncio.sleep(30)  # Send ping every 30 seconds
                try:
                    await websocket.ping()
                except Exception as e:
                    logger.error(f"Error sending ping to {stream}: {e}")
                    break
        except asyncio.CancelledError:
            # Task was cancelled, exit gracefully
            pass
        except Exception as e:
            logger.error(f"Error in ping task for {stream}: {e}")

    async def subscribe_depth(self, symbol: str, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        Subscribe to order book updates

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            callback: Callback function for depth updates
        """
        stream = f"{symbol.lower()}@depth"
        self.callbacks[stream] = callback
        await self._connect_to_stream(stream, callback)

    async def subscribe_ticker(self, symbol: str, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        Subscribe to ticker updates

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            callback: Callback function for ticker updates
        """
        stream = f"{symbol.lower()}@ticker"
        self.callbacks[stream] = callback
        await self._connect_to_stream(stream, callback)

    async def subscribe_kline(self, symbol: str, interval: str,
                             callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        Subscribe to kline/candlestick updates

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            interval: Kline interval (1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M)
            callback: Callback function for kline updates
        """
        stream = f"{symbol.lower()}@kline_{interval}"
        self.callbacks[stream] = callback
        await self._connect_to_stream(stream, callback)

    async def start(self) -> None:
        """Start WebSocket client"""
        self.running = True
        tasks = []

        # Subscribe to depth updates for all symbols
        for symbol in self.symbols:
            tasks.append(asyncio.create_task(
                self.subscribe_depth(symbol, self.callbacks.get(f"{symbol.lower()}@depth"))
            ))

        await asyncio.gather(*tasks)

    async def stop(self) -> None:
        """Stop WebSocket client"""
        self.running = False

        # Close all connections
        for stream, connection in self.connections.items():
            try:
                await connection.close()
            except Exception as e:
                logger.error(f"Error closing connection for {stream}: {e}")

        self.connections = {}

    async def monitor_connection_health(self) -> None:
        """Monitor connection health and reconnect if needed"""
        while self.running:
            try:
                current_time = asyncio.get_event_loop().time()

                # Check each connection's last heartbeat
                for stream, last_heartbeat in list(self.last_heartbeat.items()):
                    if current_time - last_heartbeat > self.heartbeat_interval * 2:
                        logger.warning(f"Connection {stream} appears stale, last heartbeat: {current_time - last_heartbeat:.1f}s ago")

                        # Try to reconnect
                        if stream in self.connections:
                            try:
                                await self.connections[stream].close()
                            except Exception as e:
                                logger.error(f"Error closing stale connection {stream}: {e}")

                            del self.connections[stream]

                        # Restart the stream
                        if stream in self.callbacks:
                            callback = self.callbacks[stream]
                            asyncio.create_task(self._connect_to_stream(stream, callback))

                await asyncio.sleep(self.heartbeat_interval)

            except Exception as e:
                logger.error(f"Error in connection health monitor: {e}")
                await asyncio.sleep(10)


class OrderBookManager:
    """Manager for order book data"""

    def __init__(self, symbols: Optional[List[str]] = None, depth: int = 10):
        """
        Initialize order book manager

        Args:
            symbols: List of trading pair symbols (e.g., ["BTCUSDT", "ETHUSDT"])
            depth: Order book depth
        """
        self.symbols = symbols or config.trading_pairs
        self.depth = depth
        self.order_books = {}
        self.last_update_ids = {}
        self.binance_client = BinanceClient(testnet=config.use_testnet)

        # Initialize order books
        for symbol in self.symbols:
            self.order_books[symbol] = {
                "bids": {},
                "asks": {},
                "last_update_id": 0
            }

    async def initialize_order_books(self) -> None:
        """Initialize order books with snapshot data"""
        for symbol in self.symbols:
            try:
                # Get order book snapshot
                snapshot = self.binance_client.get_order_book(symbol, limit=self.depth)

                # Initialize order book
                self.order_books[symbol] = {
                    "bids": {float(price): float(qty) for price, qty in snapshot["bids"]},
                    "asks": {float(price): float(qty) for price, qty in snapshot["asks"]},
                    "last_update_id": snapshot["lastUpdateId"]
                }

                self.last_update_ids[symbol] = snapshot["lastUpdateId"]

                logger.info(f"Initialized order book for {symbol}")

            except Exception as e:
                logger.error(f"Error initializing order book for {symbol}: {e}")

    async def process_depth_update(self, data: Dict[str, Any]) -> None:
        """
        Process depth update from WebSocket

        Args:
            data: Depth update data
        """
        symbol = data["s"]

        # Check if we have the order book for this symbol
        if symbol not in self.order_books:
            logger.warning(f"Received depth update for unknown symbol: {symbol}")
            return

        # Get current order book
        order_book = self.order_books[symbol]

        # Check if update is newer than our last update
        if data["u"] <= order_book["last_update_id"]:
            return

        # Update bids
        for bid in data["b"]:
            price = float(bid[0])
            quantity = float(bid[1])

            if quantity == 0:
                if price in order_book["bids"]:
                    del order_book["bids"][price]
            else:
                order_book["bids"][price] = quantity

        # Update asks
        for ask in data["a"]:
            price = float(ask[0])
            quantity = float(ask[1])

            if quantity == 0:
                if price in order_book["asks"]:
                    del order_book["asks"][price]
            else:
                order_book["asks"][price] = quantity

        # Update last update ID
        order_book["last_update_id"] = data["u"]

    def get_order_book(self, symbol: str) -> Dict[str, Any]:
        """
        Get current order book for symbol

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)

        Returns:
            Dict[str, Any]: Order book
        """
        if symbol not in self.order_books:
            return {"bids": {}, "asks": {}, "last_update_id": 0}

        return self.order_books[symbol]

    def get_best_bid_ask(self, symbol: str) -> Dict[str, float]:
        """
        Get best bid and ask for symbol

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)

        Returns:
            Dict[str, float]: Best bid and ask prices and quantities
        """
        order_book = self.get_order_book(symbol)

        bids = sorted(order_book["bids"].items(), key=lambda x: x[0], reverse=True)
        asks = sorted(order_book["asks"].items(), key=lambda x: x[0])

        best_bid = bids[0] if bids else (0, 0)
        best_ask = asks[0] if asks else (0, 0)

        return {
            "bid_price": best_bid[0],
            "bid_qty": best_bid[1],
            "ask_price": best_ask[0],
            "ask_qty": best_ask[1],
            "spread": best_ask[0] - best_bid[0] if best_ask[0] > 0 and best_bid[0] > 0 else 0,
            "spread_percent": (best_ask[0] - best_bid[0]) / best_bid[0] * 100 if best_bid[0] > 0 else 0
        }
