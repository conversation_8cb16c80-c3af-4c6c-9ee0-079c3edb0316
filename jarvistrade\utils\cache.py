"""
Caching system for JarvisTrade Lite
"""
import time
import json
import logging
from typing import Any, Dict, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry with expiration"""
    value: Any
    created_at: float
    expires_at: float
    hit_count: int = 0


class CacheManager:
    """Advanced caching system for API responses and computed data"""

    def __init__(self, default_ttl: int = 300):
        """
        Initialize cache manager

        Args:
            default_ttl: Default time-to-live in seconds
        """
        self.default_ttl = default_ttl
        self.cache: Dict[str, CacheEntry] = {}
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "total_requests": 0
        }

    def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache

        Args:
            key: Cache key

        Returns:
            Optional[Any]: Cached value if found and not expired
        """
        self.stats["total_requests"] += 1

        if key not in self.cache:
            self.stats["misses"] += 1
            return None

        entry = self.cache[key]
        current_time = time.time()

        # Check if expired
        if current_time > entry.expires_at:
            del self.cache[key]
            self.stats["misses"] += 1
            self.stats["evictions"] += 1
            return None

        # Update hit count and stats
        entry.hit_count += 1
        self.stats["hits"] += 1

        return entry.value

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Set value in cache

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (uses default if None)
        """
        ttl = ttl or self.default_ttl
        current_time = time.time()

        entry = CacheEntry(
            value=value,
            created_at=current_time,
            expires_at=current_time + ttl
        )

        self.cache[key] = entry

    def delete(self, key: str) -> bool:
        """
        Delete value from cache

        Args:
            key: Cache key

        Returns:
            bool: True if key was found and deleted
        """
        if key in self.cache:
            del self.cache[key]
            return True
        return False

    def clear(self) -> None:
        """Clear all cache entries"""
        self.cache.clear()
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "total_requests": 0
        }

    def cleanup_expired(self) -> int:
        """
        Remove expired entries

        Returns:
            int: Number of entries removed
        """
        current_time = time.time()
        expired_keys = []

        for key, entry in self.cache.items():
            if current_time > entry.expires_at:
                expired_keys.append(key)

        for key in expired_keys:
            del self.cache[key]
            self.stats["evictions"] += 1

        return len(expired_keys)

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics

        Returns:
            Dict[str, Any]: Cache statistics
        """
        total_requests = self.stats["total_requests"]
        hit_rate = (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0

        return {
            "entries": len(self.cache),
            "hits": self.stats["hits"],
            "misses": self.stats["misses"],
            "evictions": self.stats["evictions"],
            "total_requests": total_requests,
            "hit_rate_percent": round(hit_rate, 2)
        }

    def cached_call(self, key: str, func: Callable, *args, ttl: Optional[int] = None, **kwargs) -> Any:
        """
        Execute function with caching

        Args:
            key: Cache key
            func: Function to execute
            *args: Function arguments
            ttl: Cache TTL
            **kwargs: Function keyword arguments

        Returns:
            Any: Function result (cached or fresh)
        """
        # Try to get from cache first
        cached_result = self.get(key)
        if cached_result is not None:
            return cached_result

        # Execute function and cache result
        result = func(*args, **kwargs)
        self.set(key, result, ttl)

        return result


class APICache(CacheManager):
    """Specialized cache for API responses"""

    def __init__(self):
        super().__init__(default_ttl=60)  # 1 minute default for API responses

    def get_exchange_info(self, api_client, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Get exchange info with caching

        Args:
            api_client: API client instance
            force_refresh: Force refresh from API

        Returns:
            Dict[str, Any]: Exchange info
        """
        key = "exchange_info"

        if force_refresh:
            self.delete(key)

        return self.cached_call(
            key=key,
            func=api_client.get_exchange_info,
            ttl=300  # 5 minutes for exchange info
        )

    def get_account_info(self, api_client, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Get account info with caching

        Args:
            api_client: API client instance
            force_refresh: Force refresh from API

        Returns:
            Dict[str, Any]: Account info
        """
        key = "account_info"

        if force_refresh:
            self.delete(key)

        return self.cached_call(
            key=key,
            func=api_client.get_account,
            ttl=30  # 30 seconds for account info
        )

    def get_symbol_info(self, api_client, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get symbol info with caching

        Args:
            api_client: API client instance
            symbol: Trading symbol

        Returns:
            Optional[Dict[str, Any]]: Symbol info
        """
        key = f"symbol_info_{symbol}"

        def get_symbol_from_exchange():
            exchange_info = self.get_exchange_info(api_client)
            for s in exchange_info.get("symbols", []):
                if s["symbol"] == symbol:
                    return s
            return None

        return self.cached_call(
            key=key,
            func=get_symbol_from_exchange,
            ttl=600  # 10 minutes for symbol info
        )


class FeatureCache(CacheManager):
    """Specialized cache for computed features"""

    def __init__(self):
        super().__init__(default_ttl=10)  # 10 seconds default for features

    def get_orderbook_features(self, symbol: str, orderbook_hash: str,
                              compute_func: Callable, orderbook: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get orderbook features with caching

        Args:
            symbol: Trading symbol
            orderbook_hash: Hash of orderbook for cache key
            compute_func: Function to compute features
            orderbook: Orderbook data

        Returns:
            Dict[str, Any]: Computed features
        """
        key = f"features_{symbol}_{orderbook_hash}"

        return self.cached_call(
            key,
            compute_func,
            orderbook,
            ttl=5  # 5 seconds for features
        )


# Global cache instances
cache_manager = CacheManager()
api_cache = APICache()
feature_cache = FeatureCache()


def cache_decorator(ttl: int = 300):
    """
    Decorator for caching function results

    Args:
        ttl: Time-to-live in seconds

    Returns:
        Decorator function
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Create cache key from function name and arguments
            key = f"{func.__name__}_{hash(str(args) + str(sorted(kwargs.items())))}"

            return cache_manager.cached_call(key, func, *args, ttl=ttl, **kwargs)

        return wrapper
    return decorator
