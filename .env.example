# ======================================================
# JarvisTrade Lite Configuration
# ======================================================

# Binance API credentials (READ+TRADE permissions only, no withdrawal)
# Create API keys at https://www.binance.com/en/my/settings/api-management
# For testnet, create keys at https://testnet.binance.vision/
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here

# ======================================================
# Trading Settings
# ======================================================

# Trading pairs to monitor (comma-separated, no spaces)
# Recommended: 1-3 pairs for optimal performance
TRADING_PAIRS=BTCUSDT,ETHUSDT,SOLUSDT

# Use Binance testnet (True/False)
# Set to True for testing with fake money
# Set to False for live trading with real funds
USE_TESTNET=True

# Maximum position size in USD per trade
# This is the maximum amount that will be used for any single trade
MAX_POSITION_SIZE_USD=100

# Risk per trade as percentage of available balance
# Example: 1 means 1% of your available USDT balance
RISK_PER_TRADE_PERCENT=1

# Take profit percentage
# Example: 0.25 means close the trade at 0.25% profit
TAKE_PROFIT_PERCENT=0.25

# Stop loss percentage
# Example: 0.15 means close the trade at 0.15% loss
STOP_LOSS_PERCENT=0.15

# ======================================================
# Spoof Detection Settings
# ======================================================

# Minimum probability threshold for spoof detection (0.0-1.0)
# Higher values mean fewer but more confident signals
# Recommended: 0.75-0.9
SPOOF_THRESHOLD=0.85

# Minimum spread percentage for entry
# Bot will not enter trades when spread is wider than this value
# Helps avoid trading during volatile/illiquid conditions
MIN_SPREAD_ENTRY=0.08
