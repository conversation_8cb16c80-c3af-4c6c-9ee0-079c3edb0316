"""
Train spoof detection model for JarvisTrade Lite
"""
import os
import pickle
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from xgboost import XGBClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

# Create models directory if it doesn't exist
os.makedirs("jarvistrade/models/data", exist_ok=True)

# Generate synthetic training data
def generate_synthetic_data(n_samples=1000):
    """Generate synthetic data for spoof detection model training"""
    np.random.seed(42)
    
    # Features
    volume_imbalance = np.random.uniform(-0.5, 0.5, n_samples)
    spread_percent = np.random.uniform(0.01, 0.5, n_samples)
    bid_cancel_rate = np.random.uniform(0, 1, n_samples)
    ask_cancel_rate = np.random.uniform(0, 1, n_samples)
    avg_bid_lifetime = np.random.uniform(0, 60, n_samples)
    avg_ask_lifetime = np.random.uniform(0, 60, n_samples)
    avg_bid_size = np.random.uniform(0.1, 10, n_samples)
    avg_ask_size = np.random.uniform(0.1, 10, n_samples)
    
    # Create DataFrame
    df = pd.DataFrame({
        'volume_imbalance': volume_imbalance,
        'spread_percent': spread_percent,
        'bid_cancel_rate': bid_cancel_rate,
        'ask_cancel_rate': ask_cancel_rate,
        'avg_bid_lifetime': avg_bid_lifetime,
        'avg_ask_lifetime': avg_ask_lifetime,
        'avg_bid_size': avg_bid_size,
        'avg_ask_size': avg_ask_size
    })
    
    # Generate labels (spoof or not)
    # High cancel rate + short lifetime + imbalance = likely spoof
    spoof_score = (
        (df['bid_cancel_rate'] > 0.7) * 1 +
        (df['ask_cancel_rate'] > 0.7) * 1 +
        (df['avg_bid_lifetime'] < 5) * 1 +
        (df['avg_ask_lifetime'] < 5) * 1 +
        (abs(df['volume_imbalance']) > 0.3) * 1
    )
    
    # Label as spoof if score >= 3
    df['is_spoof'] = (spoof_score >= 3).astype(int)
    
    # Add some noise to make it more realistic
    noise_idx = np.random.choice(df.index, size=int(n_samples * 0.1), replace=False)
    df.loc[noise_idx, 'is_spoof'] = 1 - df.loc[noise_idx, 'is_spoof']
    
    return df

# Generate data
print("Generating synthetic training data...")
data = generate_synthetic_data(n_samples=5000)

# Split data
X = data.drop('is_spoof', axis=1)
y = data['is_spoof']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Train model
print("Training XGBoost model...")
model = XGBClassifier(
    n_estimators=100,
    max_depth=3,
    learning_rate=0.1,
    objective='binary:logistic',
    random_state=42
)
model.fit(X_train, y_train)

# Evaluate model
y_pred = model.predict(X_test)
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)

print(f"Model performance:")
print(f"Accuracy: {accuracy:.4f}")
print(f"Precision: {precision:.4f}")
print(f"Recall: {recall:.4f}")
print(f"F1 Score: {f1:.4f}")

# Save model
model_path = "jarvistrade/models/data/spoof_detector.pkl"
with open(model_path, 'wb') as f:
    pickle.dump(model, f)

print(f"Model saved to {model_path}")

# Save feature names
feature_names = list(X.columns)
with open("jarvistrade/models/data/feature_names.pkl", 'wb') as f:
    pickle.dump(feature_names, f)

print(f"Feature names saved to jarvistrade/models/data/feature_names.pkl")
print("Done!")
