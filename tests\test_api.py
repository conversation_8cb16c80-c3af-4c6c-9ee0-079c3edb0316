"""
Tests for the API module
"""
import unittest
from unittest.mock import patch, MagicMock
import json

from jarvistrade.api.binance import BinanceClient


class TestBinanceClient(unittest.TestCase):
    """Test cases for BinanceClient"""

    def setUp(self):
        """Set up test fixtures"""
        self.client = BinanceClient(
            api_key="test_key",
            api_secret="test_secret",
            testnet=True
        )

    def test_init(self):
        """Test initialization"""
        self.assertEqual(self.client.api_key, "test_key")
        self.assertEqual(self.client.api_secret, "test_secret")
        self.assertTrue(self.client.testnet)
        self.assertEqual(self.client.base_url, "https://testnet.binance.vision/api")
        self.assertEqual(self.client.ws_url, "wss://testnet.binance.vision/ws")

    @patch("jarvistrade.api.binance.requests.get")
    def test_get_exchange_info(self, mock_get):
        """Test get_exchange_info"""
        # Mock response
        mock_response = MagicMock()
        mock_response.json.return_value = {"symbols": [{"symbol": "BTCUSDT"}]}
        mock_get.return_value = mock_response

        # Call method
        result = self.client.get_exchange_info()

        # Verify
        self.assertEqual(result, {"symbols": [{"symbol": "BTCUSDT"}]})
        mock_get.assert_called_once_with(
            "https://testnet.binance.vision/api/v3/exchangeInfo",
            headers={"X-MBX-APIKEY": "test_key"},
            params={},
            timeout=10
        )

    @patch("jarvistrade.api.binance.requests.get")
    def test_get_order_book(self, mock_get):
        """Test get_order_book"""
        # Mock response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "lastUpdateId": 1,
            "bids": [["100", "1"]],
            "asks": [["101", "1"]]
        }
        mock_get.return_value = mock_response

        # Call method
        result = self.client.get_order_book("BTCUSDT", limit=10)

        # Verify
        self.assertEqual(result, {
            "lastUpdateId": 1,
            "bids": [["100", "1"]],
            "asks": [["101", "1"]]
        })
        mock_get.assert_called_once_with(
            "https://testnet.binance.vision/api/v3/depth",
            headers={"X-MBX-APIKEY": "test_key"},
            params={"symbol": "BTCUSDT", "limit": 10},
            timeout=10
        )

    @patch("jarvistrade.api.binance.time.time")
    @patch("jarvistrade.api.binance.requests.post")
    def test_create_order(self, mock_post, mock_time):
        """Test create_order"""
        # Mock time
        mock_time.return_value = 1000

        # Mock response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "symbol": "BTCUSDT",
            "orderId": 1,
            "status": "NEW"
        }
        mock_post.return_value = mock_response

        # Call method
        result = self.client.create_order(
            symbol="BTCUSDT",
            side="BUY",
            order_type="LIMIT",
            quantity=1,
            price=100,
            time_in_force="GTC"
        )

        # Verify
        self.assertEqual(result, {
            "symbol": "BTCUSDT",
            "orderId": 1,
            "status": "NEW"
        })
        mock_post.assert_called_once()


if __name__ == "__main__":
    unittest.main()
