<div align="center">
  <img src="logo.png" alt="JarvisTrade Lite Logo" width="300">
  
  # JarvisTrade Lite
  
  [![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/downloads/)
  [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
  [![Versão](https://img.shields.io/badge/Versão-1.0-green.svg)](https://github.com/dougdotccone/jarvistrade)
  [![Binance](https://img.shields.io/badge/Binance-Partner-orange.svg)](https://www.binance.com)
  
  **Um bot de trading local para Binance que detecta manipulação de orderbook e executa trades de scalping - 
  tudo na sua máquina local, sem dependências de servidor.**
  
  [📥 Download](https://github.com/dougdotccone/jarvistrade/releases) | [📚 Documentação](https://github.com/dougdotccone/jarvistrade/wiki) | [🌟 Contribuir](https://github.com/dougdotccone/jarvistrade/blob/main/CONTRIBUTING.md)
</div>

<hr style="border: 2px solid #0275d8; margin: 30px 0">

<p align="center">
  <img src="https://img.shields.io/badge/Criptografia-ChaCha20-red.svg" alt="Criptografia">
  <img src="https://img.shields.io/badge/IA-XGBoost-purple.svg" alt="IA">
  <img src="https://img.shields.io/badge/Interface-Rich-blue.svg" alt="Interface">
  <img src="https://img.shields.io/badge/Estratégia-Scalping-green.svg" alt="Estratégia">
</p>

## 📋 Índice

<div align="center">

| 🔍 [Funcionalidades](#-funcionalidades) | 🔄 [Como Funciona](#-como-funciona) | 🚀 [Instalação](#-instalação) |
|:---:|:---:|:---:|
| **⚙️ [Configuração](#%EF%B8%8F-configuração)** | **🖥️ [Uso](#%EF%B8%8F-uso)** | **🔒 [Segurança](#-segurança)** |
| **🔧 [Solução de Problemas](#-solução-de-problemas)** | **📁 [Estrutura do Projeto](#-estrutura-do-projeto)** | **📄 [Licença](#-licença)** |

</div>

## ✨ Funcionalidades

<table>
  <tr>
    <td width="50%">
      <h3>💻 100% Local</h3>
      <p>Todo o processamento acontece no seu dispositivo, sem envio de chaves API para servidores externos</p>
    </td>
    <td width="50%">
      <h3>🔍 Detecção de Manipulação</h3>
      <p>Utiliza modelo XGBoost para detectar manipulação no orderbook com alta precisão</p>
    </td>
  </tr>
  <tr>
    <td width="50%">
      <h3>⚡ Análise em Tempo Real</h3>
      <p>Monitora o orderbook em tempo real via WebSocket com resposta de milissegundos</p>
    </td>
    <td width="50%">
      <h3>🔒 Segurança Avançada</h3>
      <p>Chaves API armazenadas localmente com criptografia ChaCha20 de grau militar</p>
    </td>
  </tr>
  <tr>
    <td width="50%">
      <h3>📊 Interface Intuitiva</h3>
      <p>Interface de terminal baseada em Rich com rastreamento de PNL em tempo real e visualizações coloridas</p>
    </td>
    <td width="50%">
      <h3>🪶 Extremamente Leve</h3>
      <p>Roda em qualquer laptop com uso mínimo de CPU, otimizado para execução contínua</p>
    </td>
  </tr>
</table>

## 🔄 Como Funciona

<div align="center">
  <img src="https://via.placeholder.com/800x300?text=Fluxo+de+Funcionamento+do+JarvisTrade" alt="Fluxo de Funcionamento" width="800">
</div>

<br>

1. **📡 Dados de Mercado**: 
   > Conecta-se à API WebSocket da Binance para receber dados de orderbook em tempo real com latência mínima

2. **🧠 Detecção de Manipulação**: 
   > Analisa padrões no orderbook utilizando algoritmos avançados de ML para detectar comportamentos manipulativos

3. **📈 Estratégia de Trading**: 
   > Entra em operações contra manipulações detectadas para capturar o movimento de preço resultante

4. **⚖️ Gerenciamento de Risco**: 
   > Implementa níveis rígidos de stop-loss e take-profit para cada operação, protegendo seu capital

5. **🔐 Execução Segura**: 
   > Toda comunicação com a API acontece localmente com assinaturas HMAC para garantir autenticidade

## 🚀 Instalação

### Pré-requisitos

<details>
<summary><b>Requisitos do Sistema</b> (clique para expandir)</summary>

- **Sistema Operacional**: Windows 10/11, macOS, Linux
- **Memória RAM**: Mínimo 4GB (8GB recomendado)
- **Processador**: Intel/AMD dual core ou superior
- **Espaço em Disco**: 500MB disponíveis
- **Conexão**: Internet estável (mínimo 5 Mbps)

</details>

- 🐍 Python 3.8 ou superior
- 💰 Conta Binance com chaves API (permissões apenas de LEITURA+NEGOCIAÇÃO)

### Instalação Rápida

```bash
# Clone o repositório
git clone https://github.com/dougdotccone/jarvistrade.git

# Entre no diretório
cd jarvistrade

# Instale as dependências
pip install -r requirements.txt
```

<details>
<summary><b>Instalação com Ambiente Virtual</b> (recomendado)</summary>

```bash
# Clone o repositório
git clone https://github.com/dougdotccone/jarvistrade.git

# Entre no diretório
cd jarvistrade

# Crie um ambiente virtual
python -m venv venv

# Ative o ambiente virtual
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# Instale as dependências
pip install -r requirements.txt
```
</details>

## ⚙️ Configuração

<div align="center">
  <table>
    <tr>
      <td align="center">⚠️ <b>IMPORTANTE</b> ⚠️</td>
    </tr>
    <tr>
      <td>
        Nunca compartilhe suas chaves API com ninguém.<br>
        Use apenas permissões de LEITURA e NEGOCIAÇÃO (nunca de SAQUE).
      </td>
    </tr>
  </table>
</div>

### Configuração Básica

1️⃣ Crie um arquivo `.env` com suas chaves API da Binance (veja `.env.example`)

2️⃣ Treine o modelo de detecção (opcional, um modelo pré-treinado já está incluído):

```bash
python train_model.py
```

3️⃣ Teste a funcionalidade do bot antes de usar com dinheiro real:

```bash
python test_bot.py
```

### Parâmetros Configuráveis

<details>
<summary><b>Configurações do Arquivo .env</b> (clique para expandir)</summary>

```ini
# Credenciais API (OBRIGATÓRIO)
API_KEY=sua_chave_api_aqui
API_SECRET=seu_segredo_api_aqui

# Configuração Geral
USE_TESTNET=True                  # Use True para testar sem dinheiro real
TRADING_PAIRS=BTCUSDT,ETHUSDT     # Pares de negociação separados por vírgula
LOG_LEVEL=INFO                    # DEBUG, INFO, WARNING, ERROR

# Parâmetros de Risco
MAX_POSITION_SIZE_USD=100         # Tamanho máximo de posição em USD
RISK_PER_TRADE_PERCENT=1          # Percentual do saldo por trade (1-5%)
TAKE_PROFIT_PERCENT=0.5           # Alvo de lucro (0.1-5%)
STOP_LOSS_PERCENT=0.3             # Nível de stop loss (0.1-5%)

# Configurações de Detecção
SPOOF_THRESHOLD=0.75              # Sensibilidade de detecção (0.5-0.95)
ANALYSIS_WINDOW=15                # Janela de análise em segundos
```
</details>

## 🖥️ Uso

### Iniciando o Bot

Execute o bot com o comando:

```bash
python main.py
```

<div align="center">
  <img src="https://via.placeholder.com/800x400?text=Interface+do+Terminal+JarvisTrade" alt="Interface do Terminal" width="800">
</div>

### Controles da Interface

<kbd>T</kbd> - Ativar/desativar negociação<br>
<kbd>R</kbd> - Reiniciar conexão WebSocket<br>
<kbd>P</kbd> - Pausar/resumir atualização de tela<br>
<kbd>S</kbd> - Exibir estatísticas detalhadas<br>
<kbd>Q</kbd> - Sair da aplicação

### Primeira Execução

Na primeira execução, o JarvisTrade perguntará se você deseja criptografar seu arquivo `.env` para maior segurança:

<div align="center">
  <table>
    <tr>
      <td align="center">🔐 <b>Criptografia</b></td>
    </tr>
    <tr>
      <td>
        1. Digite uma senha forte<br>
        2. O arquivo <code>.env</code> será criptografado para <code>.env.enc</code><br>
        3. Você poderá excluir o arquivo não criptografado para maior segurança
      </td>
    </tr>
  </table>
</div>

Nas execuções subsequentes, você precisará digitar sua senha para descriptografar o arquivo `.env.enc`.

## 🔒 Segurança

<div align="center">
  <img src="https://via.placeholder.com/700x200?text=Modelo+de+Segurança+JarvisTrade" alt="Modelo de Segurança" width="700">
</div>

- **🔑 Chaves Protegidas**: Armazenadas localmente com criptografia ChaCha20
- **🛡️ Permissões Limitadas**: Apenas READ+TRADE (sem permissão de saque)
- **🏠 Processamento Local**: Nenhum dado sensível sai do seu dispositivo
- **🧠 Memória Segura**: Chaves descriptografadas apenas na RAM durante execução

## 🔧 Solução de Problemas

<details>
<summary><b>Problemas de Conexão WebSocket</b></summary>

Se você tiver problemas de conexão:

1. **Verifique sua internet** - Certifique-se de ter uma conexão estável
2. **Confirme suas chaves API** - Verifique se estão corretas e ativas
3. **Limite de taxa** - A Binance pode limitar muitas solicitações
4. **Firewall/Proxy** - Ajuste as configurações para permitir WebSockets

**Solução rápida**: Reinicie o bot com `python main.py --reconnect`
</details>

<details>
<summary><b>Sem Sinais de Negociação</b></summary>

Se você não vê nenhum sinal:

1. **Negociação habilitada?** - Pressione 'T' para ativar
2. **Threshold muito alto** - Ajuste `SPOOF_THRESHOLD` (tente 0.65)
3. **Pares incorretos** - Tente pares mais voláteis (BTCUSDT, ETHUSDT)
4. **Horário de baixa atividade** - Os mercados têm períodos de menor manipulação

**Solução rápida**: Execute `python calibrate.py` para otimizar os parâmetros
</details>

<details>
<summary><b>Erros Comuns</b></summary>

| Código | Descrição | Solução |
|--------|-----------|---------|
| `E001` | Falha na autenticação | Verifique suas chaves API |
| `E002` | Conexão perdida | Verifique sua internet e reconecte |
| `E003` | Saldo insuficiente | Adicione fundos ou reduza tamanho de posição |
| `E004` | Modelo não encontrado | Reinstale ou execute `train_model.py` |
| `E005` | Erro de criptografia | Remova `.env.enc` e reconfigure |

</details>

## 📁 Estrutura do Projeto

```
jarvistrade/
├── api/                # Clientes API e WebSocket da Binance
│   ├── binance.py      # Cliente principal da Binance
│   └── websocket.py    # Manipulador de WebSocket
├── models/             # Modelo de detecção de manipulação
│   ├── detector.py     # Detector de manipulação
│   └── features.py     # Extração de características 
├── strategies/         # Estratégias de trading
│   ├── scalper.py      # Estratégia de scalping
│   └── risk.py         # Gerenciamento de risco
├── execution/          # Execução e gerenciamento de ordens
│   ├── orders.py       # Execução de ordens
│   └── manager.py      # Gerenciador de posições
├── security/           # Utilitários de criptografia
│   └── crypto.py       # Funções de criptografia
├── ui/                 # Interface de usuário do terminal
│   ├── dashboard.py    # Painel principal
│   └── charts.py       # Componentes gráficos
├── main.py             # Ponto de entrada do programa
├── test_bot.py         # Ferramenta de teste
├── train_model.py      # Treinamento do modelo
└── requirements.txt    # Dependências
```

## 🌟 Contribuindo

Contribuições são bem-vindas! Veja como você pode ajudar:

1. 🍴 Fork o repositório
2. 🔧 Crie uma branch para sua feature (`git checkout -b feature/nova-feature`)
3. 💾 Commit suas mudanças (`git commit -m 'Adiciona nova feature'`)
4. 📤 Push para a branch (`git push origin feature/nova-feature`)
5. 🔍 Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

<div align="center">
  <br>
  <p>
    <b>JarvisTrade Lite</b> - Desenvolvido por <a href="https://github.com/dougdotccone">Douglas Costa</a> © 2023
  </p>
  <p>
    <a href="https://twitter.com/jarvistrade"><img src="https://img.shields.io/badge/Twitter-Follow-1DA1F2?style=for-the-badge&logo=twitter" alt="Twitter"></a>
    <a href="https://discord.gg/jarvistrade"><img src="https://img.shields.io/badge/Discord-Join-7289DA?style=for-the-badge&logo=discord" alt="Discord"></a>
  </p>
</div>
