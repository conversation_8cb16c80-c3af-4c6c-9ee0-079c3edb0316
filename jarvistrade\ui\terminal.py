"""
Terminal UI for JarvisTrade Lite
"""
import os
import sys
import time
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import threading

from rich.console import Console
from rich.layout import Layout
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.live import Live
from rich.prompt import Prompt, Confirm
from rich import box

from jarvistrade.config import config
from jarvistrade.api.binance import BinanceClient
from jarvistrade.api.websocket import BinanceWebSocket, OrderBookManager
from jarvistrade.models.spoof_detector import SpoofDetector
from jarvistrade.strategies.anti_spoof import AntiSpoofStrategy
from jarvistrade.execution.order_manager import OrderManager
from jarvistrade.security.encryption import EncryptionManager
from jarvistrade.alerts import AlertManager, AlertType, AlertLevel
from jarvistrade.risk import RiskManager
from jarvistrade.utils.performance import performance_monitor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("jarvistrade.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class TerminalUI:
    """Rich-based terminal UI for JarvisTrade Lite"""

    def __init__(self):
        """Initialize terminal UI"""
        self.console = Console()
        self.layout = Layout()
        self.live = None

        # Data
        self.order_books = {}
        self.spoof_detections = {}
        self.signals = []
        self.trades = []
        self.active_orders = {}
        self.pnl = 0.0

        # Components
        self.binance_client = None
        self.websocket = None
        self.order_book_manager = None
        self.spoof_detector = None
        self.strategy = None
        self.order_manager = None
        self.alert_manager = AlertManager()

        # State
        self.running = False
        self.trading_enabled = False

    def setup_layout(self) -> None:
        """Set up UI layout"""
        self.layout.split(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )

        self.layout["main"].split_row(
            Layout(name="left", ratio=1),
            Layout(name="right", ratio=1)
        )

        self.layout["left"].split(
            Layout(name="order_book", ratio=2),
            Layout(name="spoof_detection", ratio=1)
        )

        self.layout["right"].split(
            Layout(name="signals", ratio=1),
            Layout(name="trades", ratio=1),
            Layout(name="performance", ratio=1)
        )

    def render_header(self) -> Panel:
        """Render header panel"""
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        text = Text()
        text.append("JarvisTrade Lite", style="bold cyan")
        text.append(" | ")
        text.append(f"PNL: ", style="white")

        if self.pnl >= 0:
            text.append(f"${self.pnl:.2f}", style="bold green")
        else:
            text.append(f"${self.pnl:.2f}", style="bold red")

        text.append(" | ")
        text.append(f"Trading: ", style="white")

        if self.trading_enabled:
            text.append("Enabled", style="bold green")
        else:
            text.append("Disabled", style="bold red")

        text.append(" | ")
        text.append(f"Pairs: {', '.join(config.trading_pairs)}", style="white")
        text.append(" | ")
        text.append(now, style="dim")

        return Panel(text, box=box.ROUNDED)

    def render_footer(self) -> Panel:
        """Render footer panel"""
        text = Text()
        text.append("T", style="bold cyan")
        text.append("oggle Trading | ")
        text.append("Q", style="bold cyan")
        text.append("uit | ")
        text.append("Testnet: ", style="white")

        if config.use_testnet:
            text.append("Yes", style="bold yellow")
        else:
            text.append("No", style="bold green")

        return Panel(text, box=box.ROUNDED)

    def render_order_book(self) -> Panel:
        """Render order book panel"""
        if not self.order_books:
            return Panel("No order book data", title="Order Book", border_style="blue")

        tables = []

        for symbol, order_book in self.order_books.items():
            table = Table(title=symbol, box=box.SIMPLE)
            table.add_column("Price", justify="right", style="cyan")
            table.add_column("Quantity", justify="right")
            table.add_column("Total", justify="right")

            # Add asks (in reverse order)
            asks = sorted(order_book["asks"].items())
            for price, qty in asks[:5]:
                table.add_row(
                    f"{price:.2f}",
                    f"{qty:.5f}",
                    f"{price * qty:.2f}",
                    style="red"
                )

            # Add spread row
            if asks and order_book["bids"]:
                best_ask = asks[0][0] if asks else 0
                best_bid = max(order_book["bids"].keys()) if order_book["bids"] else 0
                spread = best_ask - best_bid
                spread_percent = (spread / best_bid) * 100 if best_bid > 0 else 0

                table.add_row(
                    f"Spread: {spread:.2f}",
                    f"{spread_percent:.2f}%",
                    "",
                    style="yellow"
                )

            # Add bids
            bids = sorted(order_book["bids"].items(), reverse=True)
            for price, qty in bids[:5]:
                table.add_row(
                    f"{price:.2f}",
                    f"{qty:.5f}",
                    f"{price * qty:.2f}",
                    style="green"
                )

            tables.append(table)

        return Panel(
            "\n".join(str(table) for table in tables),
            title="Order Book",
            border_style="blue"
        )

    def render_spoof_detection(self) -> Panel:
        """Render spoof detection panel"""
        if not self.spoof_detections:
            return Panel("No spoof detection data", title="Spoof Detection", border_style="magenta")

        table = Table(box=box.SIMPLE)
        table.add_column("Symbol", style="cyan")
        table.add_column("Probability", justify="right")
        table.add_column("Side", justify="center")
        table.add_column("Spread %", justify="right")

        for symbol, detection in self.spoof_detections.items():
            prob = detection["spoof_probability"]
            side = detection["side"] or "-"
            features = detection["features"]
            spread_percent = features.get("spread_percent", 0)

            # Determine row style based on probability
            if prob >= config.spoof_threshold:
                style = "bold red"
            elif prob >= 0.5:
                style = "yellow"
            else:
                style = "white"

            table.add_row(
                symbol,
                f"{prob:.2f}",
                side.upper() if side != "-" else "-",
                f"{spread_percent:.2f}%",
                style=style
            )

        return Panel(table, title="Spoof Detection", border_style="magenta")

    def render_signals(self) -> Panel:
        """Render signals panel"""
        if not self.signals:
            return Panel("No trading signals", title="Signals", border_style="yellow")

        table = Table(box=box.SIMPLE)
        table.add_column("Time", style="dim")
        table.add_column("Symbol", style="cyan")
        table.add_column("Action", justify="center")
        table.add_column("Price", justify="right")

        # Show last 10 signals
        for signal in self.signals[-10:]:
            timestamp = datetime.fromisoformat(signal["timestamp"]).strftime("%H:%M:%S")
            action = signal["action"]

            # Determine row style based on action
            style = "green" if action == "BUY" else "red"

            table.add_row(
                timestamp,
                signal["symbol"],
                action,
                f"{signal['price']:.2f}",
                style=style
            )

        return Panel(table, title="Trading Signals", border_style="yellow")

    def render_trades(self) -> Panel:
        """Render trades panel"""
        if not self.trades:
            return Panel("No trades executed", title="Trades", border_style="green")

        table = Table(box=box.SIMPLE)
        table.add_column("Time", style="dim")
        table.add_column("Symbol", style="cyan")
        table.add_column("Side", justify="center")
        table.add_column("PNL", justify="right")
        table.add_column("Type", justify="center")

        # Show last 10 trades
        for trade in self.trades[-10:]:
            exit_time = datetime.fromisoformat(trade["exit_time"]).strftime("%H:%M:%S")
            pnl = trade["pnl"]

            # Determine row style based on PNL
            style = "green" if pnl >= 0 else "red"

            table.add_row(
                exit_time,
                trade["symbol"],
                trade["side"],
                f"${pnl:.2f}",
                trade["exit_type"],
                style=style
            )

        return Panel(table, title="Trades", border_style="green")

    def render_performance(self) -> Panel:
        """Render performance panel"""
        table = Table(box=box.SIMPLE)
        table.add_column("Metric", style="cyan")
        table.add_column("Value", justify="right")

        # Get performance stats
        perf_stats = performance_monitor.get_performance_summary()

        # System metrics
        system_stats = perf_stats.get("system", {})
        if system_stats:
            cpu_current = system_stats.get("cpu", {}).get("current", 0)
            memory_current = system_stats.get("memory", {}).get("current", 0)
            uptime_hours = system_stats.get("uptime_hours", 0)

            table.add_row("CPU Usage", f"{cpu_current:.1f}%")
            table.add_row("Memory Usage", f"{memory_current:.1f}%")
            table.add_row("Uptime", f"{uptime_hours:.1f}h")

        # API counters
        counters = perf_stats.get("counters", {})
        if counters:
            table.add_row("", "")  # Separator
            for name, count in counters.items():
                display_name = name.replace("_", " ").title()
                table.add_row(display_name, str(count))

        # Risk stats if order manager exists
        if self.order_manager:
            try:
                risk_stats = self.order_manager.get_risk_stats()
                if risk_stats:
                    table.add_row("", "")  # Separator

                    risk_level = risk_stats.get("risk_level", "unknown")
                    daily_trades = risk_stats.get("daily_trades", 0)
                    consecutive_losses = risk_stats.get("consecutive_losses", 0)

                    # Color code risk level
                    risk_color = {
                        "low": "green",
                        "medium": "yellow",
                        "high": "orange",
                        "critical": "red"
                    }.get(risk_level, "white")

                    table.add_row("Risk Level", f"[{risk_color}]{risk_level.upper()}[/]")
                    table.add_row("Daily Trades", str(daily_trades))
                    table.add_row("Consecutive Losses", str(consecutive_losses))

                    # Trading status
                    can_trade = risk_stats.get("can_trade", True)
                    if not can_trade:
                        reason = risk_stats.get("trade_reason", "Unknown")
                        table.add_row("Status", f"[red]BLOCKED[/]")
                        table.add_row("Reason", f"[red]{reason}[/]")
                    else:
                        table.add_row("Status", f"[green]ACTIVE[/]")
            except Exception as e:
                table.add_row("Risk Stats", f"[red]Error: {e}[/]")

        return Panel(table, title="Performance & Risk", border_style="cyan")

    def update_display(self) -> None:
        """Update display with current data"""
        # Record system metrics
        performance_monitor.record_system_metrics()

        self.layout["header"].update(self.render_header())
        self.layout["order_book"].update(self.render_order_book())
        self.layout["spoof_detection"].update(self.render_spoof_detection())
        self.layout["signals"].update(self.render_signals())
        self.layout["trades"].update(self.render_trades())
        self.layout["performance"].update(self.render_performance())
        self.layout["footer"].update(self.render_footer())

    def handle_order_book_update(self, symbol: str, order_book: Dict[str, Any]) -> None:
        """
        Handle order book update

        Args:
            symbol: Trading pair symbol
            order_book: Order book data
        """
        self.order_books[symbol] = order_book

        # Process with spoof detector
        if self.spoof_detector:
            detection = self.spoof_detector.detect_spoof(symbol, order_book)
            self.spoof_detections[symbol] = detection

            # Process with strategy if trading enabled
            if self.strategy and self.trading_enabled:
                signal = self.strategy.process_order_book(symbol, order_book)

                if signal:
                    self.signals.append(signal)

                    # Execute signal
                    if self.order_manager:
                        self.order_manager.execute_signal(signal)

    def handle_order_update(self, update: Dict[str, Any]) -> None:
        """
        Handle order update

        Args:
            update: Order update
        """
        if "type" in update and update["type"] == "trade_completed":
            trade = update["trade"]
            self.trades.append(trade)
            self.pnl = self.order_manager.get_pnl()
        else:
            self.active_orders = self.order_manager.get_active_orders()

    async def process_depth_update(self, data: Dict[str, Any]) -> None:
        """
        Process depth update from WebSocket

        Args:
            data: Depth update data
        """
        symbol = data["s"]
        await self.order_book_manager.process_depth_update(data)
        order_book = self.order_book_manager.get_order_book(symbol)
        self.handle_order_book_update(symbol, order_book)

    async def start_websocket(self) -> None:
        """Start WebSocket connection"""
        self.websocket = BinanceWebSocket(symbols=config.trading_pairs, testnet=config.use_testnet)

        # Create tasks for each symbol
        tasks = []
        for symbol in config.trading_pairs:
            tasks.append(asyncio.create_task(
                self.websocket.subscribe_depth(symbol, self.process_depth_update)
            ))

        await asyncio.gather(*tasks)

    def keyboard_listener(self) -> None:
        """Listen for keyboard input"""
        import msvcrt  # Windows-specific keyboard input

        logger.info("Keyboard listener started. Press 'T' to toggle trading, 'Q' to quit.")

        while self.running:
            try:
                # Check if a key is available
                if msvcrt.kbhit():
                    # Read the key
                    key = msvcrt.getch().decode('utf-8').lower()

                    if key == "q":
                        logger.info("Quit command received")
                        self.running = False
                    elif key == "t":
                        self.trading_enabled = not self.trading_enabled
                        logger.info(f"Trading {'enabled' if self.trading_enabled else 'disabled'}")

                # Sleep to avoid high CPU usage
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Error in keyboard listener: {e}")
                time.sleep(1)

    async def run(self) -> None:
        """Run the terminal UI"""
        try:
            # Setup components
            self.binance_client = BinanceClient(testnet=config.use_testnet)
            self.order_book_manager = OrderBookManager(symbols=config.trading_pairs)
            self.spoof_detector = SpoofDetector()
            self.strategy = AntiSpoofStrategy(self.spoof_detector, self.binance_client)
            self.order_manager = OrderManager(self.binance_client, self.handle_order_update, self.alert_manager)

            # Initialize order books
            await self.order_book_manager.initialize_order_books()

            # Setup UI
            self.setup_layout()

            # Send system start alert
            self.alert_manager.alert_system_start()

            # Start keyboard listener
            keyboard_thread = threading.Thread(target=self.keyboard_listener)
            keyboard_thread.daemon = True
            keyboard_thread.start()

            # Start live display
            self.running = True
            with Live(self.layout, refresh_per_second=2, screen=True):
                # Initial update
                self.update_display()

                # Start WebSocket
                websocket_task = asyncio.create_task(self.start_websocket())

                # Main loop
                while self.running:
                    self.update_display()
                    await asyncio.sleep(0.5)

                # Cancel WebSocket task
                websocket_task.cancel()

        except Exception as e:
            logger.error(f"Error in UI: {e}")
            self.alert_manager.alert_error(str(e))
            raise
        finally:
            self.running = False
            self.alert_manager.alert_system_stop()


def setup_environment() -> bool:
    """
    Set up environment for JarvisTrade Lite

    Returns:
        bool: True if setup successful
    """
    console = Console()

    # Check if .env file exists
    if not os.path.exists(".env") and not os.path.exists(".env.enc"):
        console.print("[bold red]No .env or .env.enc file found.[/]")
        console.print("Creating new .env file from template...")

        # Copy .env.example to .env if it exists
        if os.path.exists(".env.example"):
            with open(".env.example", "r") as example, open(".env", "w") as env:
                env.write(example.read())

            console.print("[bold green].env file created from template.[/]")
            console.print("Please edit the .env file with your API keys and settings.")
            return False
        else:
            console.print("[bold red].env.example file not found.[/]")
            return False

    # Decrypt .env.enc if it exists and .env doesn't
    if os.path.exists(".env.enc") and not os.path.exists(".env"):
        console.print("[bold yellow]Found encrypted .env.enc file.[/]")
        password = Prompt.ask("Enter decryption password", password=True)

        if EncryptionManager.decrypt_env_file(".env.enc", ".env", password):
            console.print("[bold green].env file decrypted successfully.[/]")
        else:
            console.print("[bold red]Failed to decrypt .env.enc file.[/]")
            return False

    # Validate configuration
    if not config.validate():
        console.print("[bold red]Invalid configuration in .env file.[/]")
        console.print("Please check your API keys and settings.")
        return False

    return True


def main() -> None:
    """Main entry point for JarvisTrade Lite"""
    console = Console()

    try:
        # Show welcome message
        console.print("[bold cyan]JarvisTrade Lite[/]", justify="center")
        console.print("A local trading bot for Binance", justify="center")
        console.print("")

        # Setup environment
        if not setup_environment():
            console.print("[bold red]Setup failed. Exiting...[/]")
            return

        # Ask to encrypt .env file if not already encrypted
        if os.path.exists(".env") and not os.path.exists(".env.enc"):
            if Confirm.ask("Do you want to encrypt your .env file?"):
                password = Prompt.ask("Enter encryption password", password=True)
                password_confirm = Prompt.ask("Confirm password", password=True)

                if password != password_confirm:
                    console.print("[bold red]Passwords do not match.[/]")
                else:
                    if EncryptionManager.encrypt_env_file(".env", ".env.enc", password):
                        console.print("[bold green].env file encrypted successfully.[/]")

                        if Confirm.ask("Do you want to delete the unencrypted .env file?"):
                            os.remove(".env")
                            console.print("[bold green]Unencrypted .env file deleted.[/]")
                    else:
                        console.print("[bold red]Failed to encrypt .env file.[/]")

        # Start terminal UI
        console.print("[bold green]Starting JarvisTrade Lite...[/]")
        ui = TerminalUI()
        asyncio.run(ui.run())

    except KeyboardInterrupt:
        console.print("[bold yellow]Interrupted by user. Exiting...[/]")
    except Exception as e:
        console.print(f"[bold red]Error: {e}[/]")
        logger.exception("Unhandled exception")


if __name__ == "__main__":
    main()
