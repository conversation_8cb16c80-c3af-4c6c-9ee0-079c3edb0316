"""
Encryption utilities for JarvisTrade Lite
"""
import os
import base64
import getpass
from typing import <PERSON>ple, Optional
from Crypto.Cipher import ChaCha20_Poly1305
from Crypto.Protocol.KDF import scrypt


class EncryptionManager:
    """Manager for encrypting and decrypting sensitive data"""
    
    @staticmethod
    def derive_key(password: str, salt: Optional[bytes] = None) -> Tuple[bytes, bytes]:
        """
        Derive encryption key from password
        
        Args:
            password: User password
            salt: Salt for key derivation (generated if None)
            
        Returns:
            Tuple[bytes, bytes]: (key, salt)
        """
        if salt is None:
            salt = os.urandom(16)
        
        key = scrypt(
            password.encode('utf-8'),
            salt=salt,
            key_len=32,
            N=2**14,
            r=8,
            p=1
        )
        
        return key, salt
    
    @staticmethod
    def encrypt_env_file(input_file: str, output_file: str, password: Optional[str] = None) -> bool:
        """
        Encrypt .env file with ChaCha20-Poly1305
        
        Args:
            input_file: Path to input .env file
            output_file: Path to output encrypted file
            password: Encryption password (prompted if None)
            
        Returns:
            bool: True if encryption successful
        """
        try:
            # Read input file
            with open(input_file, 'rb') as f:
                plaintext = f.read()
            
            # Get password if not provided
            if password is None:
                password = getpass.getpass("Enter encryption password: ")
                password_confirm = getpass.getpass("Confirm password: ")
                if password != password_confirm:
                    print("Passwords do not match")
                    return False
            
            # Generate salt and derive key
            key, salt = EncryptionManager.derive_key(password)
            
            # Generate nonce
            nonce = os.urandom(12)
            
            # Create cipher
            cipher = ChaCha20_Poly1305.new(key=key, nonce=nonce)
            
            # Encrypt data
            ciphertext, tag = cipher.encrypt_and_digest(plaintext)
            
            # Write encrypted data
            with open(output_file, 'wb') as f:
                f.write(salt)
                f.write(nonce)
                f.write(tag)
                f.write(ciphertext)
            
            return True
        
        except Exception as e:
            print(f"Encryption error: {e}")
            return False
    
    @staticmethod
    def decrypt_env_file(input_file: str, output_file: str, password: Optional[str] = None) -> bool:
        """
        Decrypt .env file with ChaCha20-Poly1305
        
        Args:
            input_file: Path to input encrypted file
            output_file: Path to output .env file
            password: Decryption password (prompted if None)
            
        Returns:
            bool: True if decryption successful
        """
        try:
            # Read encrypted file
            with open(input_file, 'rb') as f:
                data = f.read()
            
            # Extract components
            salt = data[:16]
            nonce = data[16:28]
            tag = data[28:44]
            ciphertext = data[44:]
            
            # Get password if not provided
            if password is None:
                password = getpass.getpass("Enter decryption password: ")
            
            # Derive key
            key, _ = EncryptionManager.derive_key(password, salt)
            
            # Create cipher
            cipher = ChaCha20_Poly1305.new(key=key, nonce=nonce)
            
            # Decrypt data
            try:
                plaintext = cipher.decrypt_and_verify(ciphertext, tag)
            except ValueError:
                print("Invalid password or corrupted data")
                return False
            
            # Write decrypted data
            with open(output_file, 'wb') as f:
                f.write(plaintext)
            
            return True
        
        except Exception as e:
            print(f"Decryption error: {e}")
            return False
