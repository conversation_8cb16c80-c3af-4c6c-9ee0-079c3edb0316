"""
Main entry point for JarvisTrade Lite
"""
import logging
import sys
from jarvistrade.ui.terminal import main

# Setup advanced logging
try:
    from jarvistrade.utils.logging_config import setup_logging
    setup_logging(log_level="INFO", enable_json_logs=False)
except ImportError:
    # Fallback to basic logging if advanced logging not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler("jarvistrade_debug.log")
        ]
    )

if __name__ == "__main__":
    print("Starting JarvisTrade Lite...")
    try:
        main()
    except Exception as e:
        print(f"Error: {e}")
        logging.exception("Unhandled exception")
