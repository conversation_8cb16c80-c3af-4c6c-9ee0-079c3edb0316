"""
Integration tests for JarvisTrade Lite
"""
import unittest
import asyncio
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from jarvistrade.config import Config
from jarvistrade.api.binance import BinanceClient
from jarvistrade.api.websocket import BinanceWebSocket, OrderBookManager
from jarvistrade.models.spoof_detector import SpoofDetector
from jarvistrade.strategies.anti_spoof import AntiSpoofStrategy
from jarvistrade.execution.order_manager import OrderManager
from jarvistrade.alerts import AlertManager, AlertType, AlertLevel


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete trading system"""

    def setUp(self):
        """Set up test fixtures"""
        # Create temporary config
        self.temp_dir = tempfile.mkdtemp()
        self.config = Config()
        self.config.api_key = "test_api_key"
        self.config.api_secret = "test_api_secret"
        self.config.use_testnet = True
        self.config.trading_pairs = ["BTCUSDT"]
        self.config.max_position_size_usd = 100
        self.config.risk_per_trade_percent = 1
        self.config.spoof_threshold = 0.8

        # Mock components
        self.binance_client = Mock(spec=BinanceClient)
        self.alert_manager = AlertManager(log_alerts=False, save_to_file=False)

    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_order_manager_validation_flow(self):
        """Test order manager validation flow"""
        # Setup mocks
        self.binance_client.get_exchange_info.return_value = {
            "symbols": [{
                "symbol": "BTCUSDT",
                "status": "TRADING",
                "filters": [{
                    "filterType": "LOT_SIZE",
                    "minQty": "0.00001",
                    "maxQty": "1000",
                    "stepSize": "0.00001"
                }]
            }]
        }

        self.binance_client.get_account.return_value = {
            "balances": [{
                "asset": "USDT",
                "free": "1000.0",
                "locked": "0.0"
            }]
        }

        # Create order manager
        order_manager = OrderManager(
            binance_client=self.binance_client,
            alert_manager=self.alert_manager
        )

        # Test validation methods
        self.assertTrue(order_manager._validate_trading_conditions("BTCUSDT", "BUY", 50000))
        self.assertFalse(order_manager._validate_trading_conditions("ETHUSDT", "BUY", 50000))  # Not in trading pairs
        self.assertFalse(order_manager._validate_trading_conditions("BTCUSDT", "INVALID", 50000))  # Invalid side
        self.assertFalse(order_manager._validate_trading_conditions("BTCUSDT", "BUY", -100))  # Invalid price

        # Test order parameter validation
        self.assertTrue(order_manager._validate_order_parameters("BTCUSDT", "BUY", 0.00001))  # Exact min
        self.assertFalse(order_manager._validate_order_parameters("BTCUSDT", "BUY", 0.000001))  # Below min

    def test_spoof_detector_integration(self):
        """Test spoof detector integration"""
        detector = SpoofDetector()

        # Create sample order book
        order_book = {
            "bids": {50000: 1.0, 49999: 2.0, 49998: 1.5},
            "asks": {50001: 1.0, 50002: 2.0, 50003: 1.5},
            "last_update_id": 12345
        }

        # Test detection
        result = detector.detect_spoof("BTCUSDT", order_book)

        # Verify result structure
        self.assertIn("spoof_probability", result)
        self.assertIn("side", result)
        self.assertIn("features", result)
        self.assertIsInstance(result["spoof_probability"], (int, float))

    def test_strategy_signal_generation(self):
        """Test strategy signal generation"""
        # Create mocks
        spoof_detector = Mock(spec=SpoofDetector)
        binance_client = Mock(spec=BinanceClient)

        # Setup spoof detection result
        spoof_detector.detect_spoof.return_value = {
            "spoof_probability": 0.9,
            "side": "bid",
            "features": {
                "spread_percent": 0.05,
                "best_bid": 50000,
                "best_ask": 50001
            }
        }

        # Create strategy
        strategy = AntiSpoofStrategy(spoof_detector, binance_client)

        # Create sample order book
        order_book = {
            "bids": {50000: 1.0},
            "asks": {50001: 1.0},
            "last_update_id": 12345
        }

        # Test signal generation
        signal = strategy.process_order_book("BTCUSDT", order_book)

        # Verify signal
        self.assertIsNotNone(signal)
        self.assertEqual(signal["symbol"], "BTCUSDT")
        self.assertEqual(signal["action"], "SELL")  # Counter to bid spoofing
        self.assertIn("spoof_probability", signal)
        self.assertIn("timestamp", signal)

    def test_alert_system_integration(self):
        """Test alert system integration"""
        alerts_received = []

        def alert_callback(alert):
            alerts_received.append(alert)

        # Register callback
        self.alert_manager.register_callback(AlertType.TRADE_EXECUTED, alert_callback)

        # Send test alert
        self.alert_manager.alert_trade_executed("BTCUSDT", "BUY", 0.001, 50000)

        # Verify alert was received
        self.assertEqual(len(alerts_received), 1)
        alert = alerts_received[0]
        self.assertEqual(alert.type, AlertType.TRADE_EXECUTED)
        self.assertEqual(alert.level, AlertLevel.INFO)
        self.assertIn("BTCUSDT", alert.message)

    @patch('jarvistrade.execution.order_manager.OrderManager._validate_trading_conditions')
    @patch('jarvistrade.execution.order_manager.OrderManager._validate_order_parameters')
    def test_complete_trading_flow(self, mock_validate_params, mock_validate_conditions):
        """Test complete trading flow from signal to execution"""
        # Setup mocks
        mock_validate_conditions.return_value = True
        mock_validate_params.return_value = True

        self.binance_client.create_order.return_value = {
            "orderId": 12345,
            "status": "FILLED",
            "executedQty": "0.001",
            "price": "50000"
        }

        # Create order manager with fresh risk manager
        from jarvistrade.risk import RiskManager
        fresh_risk_manager = RiskManager()
        fresh_risk_manager.update_balance(10000.0)  # Set high balance

        order_manager = OrderManager(
            binance_client=self.binance_client,
            alert_manager=self.alert_manager,
            risk_manager=fresh_risk_manager
        )

        # Create trading signal
        signal = {
            "symbol": "BTCUSDT",
            "action": "BUY",
            "price": 50000,
            "spoof_probability": 0.9,
            "spoof_side": "ask"
        }

        # Execute signal
        with patch.object(order_manager, '_calculate_position_size', return_value=0.001):
            with patch.object(order_manager, '_place_take_profit_stop_loss'):
                result = order_manager.execute_signal(signal)

        # Verify execution
        self.assertIsNotNone(result)
        self.assertEqual(result["symbol"], "BTCUSDT")
        self.assertEqual(result["side"], "BUY")
        self.assertEqual(result["order_id"], 12345)

        # Verify order was stored
        self.assertIn(12345, order_manager.active_orders)

    def test_error_handling_and_recovery(self):
        """Test error handling and recovery mechanisms"""
        # Test API error handling
        self.binance_client.get_account.side_effect = Exception("API Error")

        order_manager = OrderManager(
            binance_client=self.binance_client,
            alert_manager=self.alert_manager
        )

        # This should not crash and should return False
        result = order_manager._validate_trading_conditions("BTCUSDT", "BUY", 50000)
        self.assertFalse(result)

        # Test signal execution with API error
        self.binance_client.create_order.side_effect = Exception("Order failed")

        signal = {
            "symbol": "BTCUSDT",
            "action": "BUY",
            "price": 50000
        }

        result = order_manager.execute_signal(signal)
        self.assertIsNone(result)

    def test_configuration_validation(self):
        """Test configuration validation"""
        # Test valid config
        config = Config()
        config.api_key = "test_key"
        config.api_secret = "test_secret"
        config.trading_pairs = ["BTCUSDT"]

        self.assertTrue(config.validate())

        # Test invalid config - missing API key
        config.api_key = ""
        self.assertFalse(config.validate())

        # Test invalid config - empty trading pairs
        config.api_key = "test_key"
        config.trading_pairs = []
        self.assertFalse(config.validate())

    async def test_websocket_connection_health(self):
        """Test WebSocket connection health monitoring"""
        # Create WebSocket client
        ws_client = BinanceWebSocket(symbols=["BTCUSDT"], testnet=True)

        # Test heartbeat initialization
        self.assertEqual(len(ws_client.last_heartbeat), 0)

        # Simulate heartbeat update
        import time
        current_time = time.time()
        ws_client.last_heartbeat["btcusdt@depth"] = current_time

        # Verify heartbeat was recorded
        self.assertIn("btcusdt@depth", ws_client.last_heartbeat)
        self.assertEqual(ws_client.last_heartbeat["btcusdt@depth"], current_time)


if __name__ == "__main__":
    unittest.main()
